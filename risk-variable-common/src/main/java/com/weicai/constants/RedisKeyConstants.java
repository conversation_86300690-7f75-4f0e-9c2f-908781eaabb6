package com.weicai.constants;

/**
 * redis 缓存key
 * <AUTHOR>
 * @date 2023年06月10日 下午2:16
 */
public class RedisKeyConstants {

    /** 变量组缓存key */
    public static final String VAR_GROUP_MAP_KEY = "var_group_map:";
    /** 变量缓存key */
    public static final String DATASOURCE_FULL_INFO_KEY = "datasource_full_info_key:";
    public static final String VAR_INFO_MAP_KEY = "var_info_map:";
    public static final String DATASOURCE_DEPEND_VARS_KEY = "datasource_depend_vars_map:";

    public static final String REQUEST_TIME_COUNT = "requestTimeCount:";
    public static final String REQUEST_USER = "requestUser:";
    public static final String REQUEST_CATEGORY_FIRST = "requestFirstCategory:";
    public static final String REQUEST_CATEGORY_SECOND = "requestSecondCategory:";
    public static final String REQUEST_TAG = "requestTag:";
    /** 变量创建时间缓存key */
    public static final String VAR_CACHE_TIME = ":cache_in_time";
    /** 衍生变量对应组缓存key */
    public static final String DERIVE_VAR_GROUP = "derive_group:";
    /** 数据源模版 **/
    public static final String DATASOURCE_TEMPLATE_INFO_KEY = "datasource_template_info_key:";
    /** 同源变量 **/
    public static final String VAR_SAME_SOURCE = "var_same_source:";

    /*************************** 变量组的KEY **************************/
    public static final String VARIABLE_GROUP_SET_LOCK = "variable_group_set_lock:";
    public static final String VARIABLE_LIST_SET_LOCK = "variable_list_set_lock:";
    public static final String VARIABLE_SET_LOCK = "variable_set_lock:";
    public static final String VARIABLE_DATASOURCE_TEMPLATE_SET_LOCK = "variable_datasource_set_lock:";

    public static final String DATASOURCE_SET_LOCK = "datasource_set_lock:";
    public static final String DATASOURCE_TEMPLATE_SET_LOCK = "datasource_template_set_lock:";
    public static final String VARIABLE_SOURCE_SET_LOCK = "variable_source_set_lock:";

    public static final String DATASOURCE_CACHE_SET_LOCK = "datasource_set_lock:";



    /***************************数据源相关******************************/
    public static final String VARIABLE_RESOURCE_CONTENT = "content:";

    public static final String VARIABLE_RESOURCE = "variableResource:";

    public static final String VARIABLE_SOURCE_TEMPLATE = "variableSourceTemplate:";

    public static final String VARIABLE_REQUEST_DORIS_COST_TIME_COUNT = "variable_request_doris_cost_time_count";
    public static final String VARIABLE_REQUEST_OFFLINE_DORIS_COST_TIME_COUNT = "variable_request_offline_doris_cost_time_count";
    public static final String VARIABLE_REQUEST_DORIS_SWITCH = "variable_request_doris_switch";
    public static final String VARIABLE_REQUEST_OFFLINE_DORIS_SWITCH = "variable_request_offline_doris_switch";

    /** 新的耗时看板RedisKey **/
    public static final String VARIABLE_REQUEST_DORIS_COST_TIME_COUNT_NEW = "variable_request_doris_cost_time_count_new";
    public static final String VARIABLE_REQUEST_DORIS_COST_TIME_COUNT_NEW_GROUP = "variable_request_doris_cost_time_count_new_group";

    public static final String VARIABLE_REQUEST_OFFLINE_DORIS_COST_TIME_COUNT_NEW = "variable_request_offline_doris_cost_time_count_new";
    public static final String VARIABLE_REQUEST_OFFLINE_DORIS_COST_TIME_COUNT_NEW_GROUP = "variable_request_offline_doris_cost_time_count_new_group";

    public static final String VARIABLE_REQUEST_DORIS_SWITCH_NEW = "variable_request_doris_switch_new";
    public static final String VARIABLE_REQUEST_OFFLINE_DORIS_SWITCH_NEW = "variable_request_offline_doris_switch_new";

    public static final String DERIVE_TASK = "derive_task:";

    public static final String DERIVE_RELATION_TASK = "derive_relation_task:";

    public static final String DERIVE_RELATION_TASK_HASH = "derive_relation_task_hash:";

    public static final String VARIABLE_SOURCE_RESULT = "variableSourceResult:";

    public static final String VARIABLE_SOURCE_JOBID = "variableSourceJobId:";

    public static final String SERVICE_TYPE = "serviceType:";

    public static final String SERVICE_CACHE = "SERVICE_CACHE:";

    public static final String DWAPI_DATA_CACHE = "DWAPI_DATA_CACHE:";
    public static final String MODE_SYNC = "SYNC";

    public static final String MONITOR_VAR_MISS_COUNT = "MONITOR_VAR_MISS_COUNT:";

    public static final String MONITOR_VAR_MISS_COUNT_NEW = "MONITOR_VAR_MISS_COUNT_NEW:";

    public static final String REQUEST_ID_LOCK_KEY = "requestIdLockKey:";

    /** 告警打点*/
    public static final String NEW_MONITOR_VAR_MISS_COUNT = "NEW_MONITOR_VAR_MISS_COUNT:";

    /** 人工审核状态缓存 */
    public static final String MANUAL_AUDIT_STATUS = "manual_audit_status:";

}
