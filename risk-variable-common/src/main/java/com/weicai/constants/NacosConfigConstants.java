package com.weicai.constants;

/**
 * nacos key
 * <AUTHOR>
 * @date 2024年03月30日 20:34
 */
public class NacosConfigConstants {
    //实时变量doris url
    public static final String DWAPI_REAL_TIME_URL = "dwapi.realtime.doris.url";
    //静态变量doris url
    public static final String DWAPI_OFFLINE_DORIS_URL = "dwapi.offline.doris.url";

    public static final String LOG_DETAIL = "log.detail";

    public static final String ALL_FLOW = "all.flow";

    public static final String SPLIT_FLOW = "split.flow";

    public static final String VAR_MONITOR = "monitor.var";

    public static final String LOG_UNIFY_DETAIL = "log.request.detail";

    // 是否开启发送随机数到dc
    public static final String SEND_RANDOM_FLAG_FLAG = "send.random.data.flag";

    /**
     * 变量缓存时间和dwapi数据源缓存时间,小时
     * <AUTHOR>
     * @date 2024/5/20 17:22
     * @param null
     * @return null
     */
    public static final String VAR_CACHE_TIMEOUT_HOURS = "var.cache.timeout.hours";
    public static final String DATASOURCE_DEPEND_VAR_CACHE_TIMEOUT_HOURS = "datasource.depend.var.cache.timeout.hours";
    /** topic对应的数据源 */
    public static final String TOPICS_MAPPING_SERVICE_CODE_MAP = "topics.mapping.serviceCode.map";


    // 卡单需要写入mongo缓存的变量
    public static String NEED_CACHED_TO_MONGO_VARS = "need.cached.to.mongo.vars";
    // 同步返回，外层添加data
    public static final String NEED_PUT_DATA_SERVICE_CODES = "need.put.data.serviceCodes";

    // 批量事件code
    public static final String BATCH_EVENTS_CODES = "batch.event.codes";

    // 变量监控忽略告警数据源
    public static final String VAR_MONITOR_IGNORE_SERVICE_CODES = "var.monitor.ignore.service.codes";

    // 变量监控忽略告警数据源
    public static final String IGNORE_MODEL_CODES = "ignore.model.codes";

    // 忽略的变量
    public static final String VAR_MONITOR_IGNORE_CODES = "var.monitor.ignore.codes";

    public static final String VIEW_VAR = "view.var";

    public static final String NEED_WAIT_ALL_TO_SEND_EVENTS = "need.waitAllToSend.events";

    // 征信白盒告警信息通知人
    public static final String CREDIT_WECHAT_ALERT_MOBILE_LIST = "credit.wechat.alert.mobile.list";

    // 征信分
    public static final String CREDIT_YXGRADE = "credit.yxGrade";

    // 鸿飞加密域
    public static final String HFCREDIT_VARS = "hfCredit.vars";

    // 征信数据源
    public static final String CREDIT_DATASOURCES = "credit.datasources";

    /**需要埋点监控的事件*/
    public static final String NEED_MONITOR_EVENT_CODES = "need.monitor.event.codes";

    /**切换使用resource 得url进行调用*/
    public static final String SWITCH_DWAPI_URL_FLAG = "switch.dwapi.url.flag";

    /**不卡单报错*/
    public static final String NO_NEED_THROW_EXCEPTION_EVENT_AND_SERVICE_MAP = "no.need.throwex.event.services.map";

    // 不卡单配置by步骤
    public static final String NO_NEED_THROW_EXCEPTION_EVENT_AND_SERVICE_BY_STEP_CONFIG = "no.need.throwex.event.services.by.step.config";

    // 不写入缓存的事件
    public static final String NO_CACHED_EVENT_CODES="nocache.event.codes";

    /** 重试需要变更jobId的服务*/
    public static final String NEED_CHANGE_JOBID_SERVICE_CODES="need.change.jobid.service.codes";

    /**资源包提醒*/
    public static final String RESOURCE_MENTIONED_LIST= "resource.mentioned.list";
    public static final String RESOURCE_ROBOT_KEY= "resource.robot.key";
    /** // 资源包map key：资源包code, v:类型*/
    public static final String RESOURCE_USERKEY_TYPE_MAP= "resource.userkey.type.map";
}
