<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weicai.mapper.di.RaNetworkRelatedNodeRecordMapper">
  <resultMap id="BaseResultMap" type="com.weicai.entity.po.RaNetworkRelatedNodeRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source_system" jdbcType="VARCHAR" property="sourceSystem" />
    <result column="from_node_id" jdbcType="VARCHAR" property="fromNodeId" />
    <result column="from_node_code" jdbcType="VARCHAR" property="fromNodeCode" />
    <result column="related_node_id" jdbcType="VARCHAR" property="relatedNodeId" />
    <result column="related_node_code" jdbcType="VARCHAR" property="relatedNodeCode" />
    <result column="call_type" jdbcType="VARCHAR" property="callType" />
    <result column="call_status" jdbcType="VARCHAR" property="callStatus" />
    <result column="user_tag" jdbcType="VARCHAR" property="userTag" />
    <result column="intermediary_tag" jdbcType="VARCHAR" property="intermediaryTag" />
    <result column="record_content" jdbcType="VARCHAR" property="recordContent" />
    <result column="user_key" jdbcType="VARCHAR" property="userKey" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, source_system, from_node_id, from_node_code, related_node_id, related_node_code, 
    call_type, call_status, user_tag, intermediary_tag, record_content, user_key, remark, 
    create_by, update_by, create_time, update_time, del_flag
  </sql>

</mapper>