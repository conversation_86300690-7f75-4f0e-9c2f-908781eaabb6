<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weicai.mapper.di.RaNetworkNodeTagMapper">
  <resultMap id="BaseResultMap" type="com.weicai.entity.po.RaNetworkNodeTagDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source_system" jdbcType="VARCHAR" property="sourceSystem" />
    <result column="node_id" jdbcType="VARCHAR" property="nodeId" />
    <result column="node_code" jdbcType="VARCHAR" property="nodeCode" />
    <result column="tag_code" jdbcType="VARCHAR" property="tagCode" />
    <result column="tag_desc" jdbcType="VARCHAR" property="tagDesc" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, source_system, node_id, node_code, tag_code, tag_desc, remark, create_by, update_by, 
    create_time, update_time, del_flag
  </sql>

</mapper>