package com.weicai.entity.vo;

import com.weicai.entity.dto.NetworkNodeDTO;
import com.weicai.entity.dto.NetworkNodeLineDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 关系网络主表查询入参
 * Date: 2025/5/22
 */
@Data
public class NetworkNodeVO {
    /**
     * 根节点id
     */
    private String rootId;

    /**
     * 节点
     */
    private List<NetworkNodeDTO> nodes;
    /**
     * 边信息
     */
    private List<NetworkNodeLineDTO> lines;

    /**
     *懒加载标识，1:懒加载，0：非懒加载，默认是0
     */
    private Integer lazyTag;
}
