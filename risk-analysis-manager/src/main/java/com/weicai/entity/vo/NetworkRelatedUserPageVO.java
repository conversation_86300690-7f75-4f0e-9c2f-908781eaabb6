package com.weicai.entity.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @description: 关系网络主表查询入参
 * Date: 2025/5/22
 */
@Data
public class NetworkRelatedUserPageVO {
    /**
     * 节点id
     */
    private String nodeId;
    /**
     * 节点编码
     */
    private String nodeCode;
    /**
     * 节点编码（就是 ID_CARD、PHONE、DEVICE的值）
     */
    private String nodeType;
    /**
     * 最早关联时间（格式 YYYY-MM-DD HH:mm:ss）
     */
    private String firstAssociatedTime;
    /**
     * 最近关联时间（格式 YYYY-MM-DD HH:mm:ss）
     */
    private String lastAssociatedTime;
    /**
     * 姓名
     */
    private String name;
    /**
     * 反欺诈记录数
     */
    private Integer recordCount;
    /**
     * 根节点id
     */
    private String userKey;
    /**
     * 欺诈原因
     */
    private String fraudReason;
}
