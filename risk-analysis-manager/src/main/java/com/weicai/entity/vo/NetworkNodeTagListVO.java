package com.weicai.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @description: 关系网络主表查询入参
 * Date: 2025/5/22
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class NetworkNodeTagListVO {
    private Long id;
    /**
     * 节点id
     */
    private String nodeId;
    /**
     * 节点code
     */
    private String nodeCode;
    /**
     * 标签code
     */
    private String tagCode;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;

}
