package com.weicai.entity.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 人工审核状态查询响应VO
 * 
 * <AUTHOR>
 * @since 2024/12/26
 */
@Data
@Accessors(chain = true)
public class ManualAuditStatusVO {
    
    /**
     * 用户标识
     */
    private String userKey;
    
    /**
     * 是否正在人工审核中
     */
    private Boolean isInManualAudit;
    
    /**
     * 状态描述
     */
    private String statusDesc;
    
    /**
     * 创建成功响应
     */
    public static ManualAuditStatusVO success(String userKey, boolean isInManualAudit) {
        return new ManualAuditStatusVO()
                .setUserKey(userKey)
                .setIsInManualAudit(isInManualAudit)
                .setStatusDesc(isInManualAudit ? "正在人工审核中" : "未在人工审核中");
    }
}
