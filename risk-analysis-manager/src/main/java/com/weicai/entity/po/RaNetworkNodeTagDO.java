package com.weicai.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * ra_network_node_tag
 * <AUTHOR>
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ra_network_node_tag")
public class RaNetworkNodeTagDO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 系统
     */
    private String sourceSystem;

    /**
     * 节点id
     */
    private String nodeId;

    /**
     * 节点编码
     */
    private String nodeCode;

    /**
     * 节点标签code1.团伙；2.疑似团伙；3.清白；4.其他情况； 5. 中介
     */
    private String tagCode;

    /**
     * 节点标签描述
     */
    private String tagDesc;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 修改者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识
     */
    private Integer delFlag;

    private static final long serialVersionUID = 1L;
}