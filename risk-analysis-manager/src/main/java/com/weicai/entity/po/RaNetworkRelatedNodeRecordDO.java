package com.weicai.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * ra_network_related_node_record
 * <AUTHOR>
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ra_network_related_node_record")
public class RaNetworkRelatedNodeRecordDO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 系统
     */
    private String sourceSystem;

    /**
     * 节点id
     */
    private String fromNodeId;

    /**
     * 节点编码
     */
    private String fromNodeCode;

    /**
     * 节点id
     */
    private String relatedNodeId;

    /**
     * 节点编码
     */
    private String relatedNodeCode;

    /**
     * 拨打模板，0：固话；1：手机
     */
    private String callType;

    /**
     * 拨打状态0. 致电接通；1.致电无人接听；2.致电正在通话；3.致电停机；4.致电关机；5.致电空号；6.致电忙音；7.致电呼叫转移；8.致电接通后挂断
     */
    private String callStatus;

    /**
     * 用户标签1.外部高风险；2.团伙欺诈；3.伪冒用户；4.中介引导（风险较高）；5.中介引导（风险较低）；6.关联欺诈；7.清白
     */
    private String userTag;

    /**
     * 中介标签1.不良中介；2.一般中介；3.非中介
     */
    private String intermediaryTag;

    /**
     * 记录内容
     */
    private String recordContent;

    /**
     * user_key
     */
    private String userKey;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 修改者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识
     */
    private Integer delFlag;

    private static final long serialVersionUID = 1L;
}