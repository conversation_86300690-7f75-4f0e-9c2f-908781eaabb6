package com.weicai.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Set;

/**
 * <AUTHOR>
 * @description: 关系网络主表查询入参
 * Date: 2025/5/22
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NetworkNodeQueryDTO {
    /**
     * 搜索的节点类型（ ID_CARD、PHONE、DEVICE
     */
    @NotBlank(message = "节点类型不能为空.")
    private String nodeType;

    /**
     * 节点编码
     */
    @NotBlank(message = "节点编码不能为空.")
    private String nodeCode;
    /**
     * 查询层级（如 1 表示只查直接关联，2 查 2 跳内关联）默认是4
     */
    private String maxHops = "4";
    /**
     * 是否加密查询，0：不加密，1：加密
     */
    private Integer needDecrypt;
    /**
     * 设备类型，device,android,imei,wifimac
     */
    private String deviceType;
    /**
     *懒加载标识，1:懒加载，0：非懒加载，默认是0
     */
    private Integer lazyTag = 0;

    private Set<String> relatedNodeIds;
}
