package com.weicai.entity.dto;

import lombok.Data;

/**
 *             {
 *                 "from": "aa98a36bd53fcf5131d5771939124ff3",
 *                 "to": "9454000A821B0787AA812AFE28EB5FC3",
 *                 "text": "BANK_REGISTERED",
 *                 "create_time": "2025-04-28 09:32:04"
 *             }
 * <AUTHOR>
 * @description: TODO
 * Date: 2025/5/28
 */
@Data
public class NetworkNodeLineDTO {
    private String from;
    private String to;
    private String text;
    private String create_time;
}
