package com.weicai.entity.dto;

import com.weicai.annotation.FieldMapping;
import com.weicai.annotation.Sensitive;
import com.weicai.enumeration.AntiFraudDetailEnum;
import com.weicai.service.sensitive.SensitiveStrategy;
import lombok.Data;


/**
 * 反欺诈调查明细
 */
@Data
public class AntiFraudDetailDTO {
    /**
     * 采集时间
     */
    @FieldMapping(date = true,value = "uploadInfoOpportunity")
    private String httpTimestamp;
    /**
     * 采集位置/页面
     */
    private String uploadInfoOpportunity;
    /**
     * 版本号
     */
    private String version;
    /**
     * 是否授权：1-NULL 2-授权 3-已拒绝
     */
    @FieldMapping(enumClass = AntiFraudDetailEnum.IsAuthorizedEnum.class,value = "isAuthorized")
    private String isAuthorized;

    /**
     * 设备平台：iso;Android
     */
    private String os;

    @Data
    public static class UploadPhoneBookDetailDTO {
        /**
         * 姓名
         */
        private String name;

        /**
         * 手机号码
         */
        private String mobile;

        /**
         * 公司名称
         */
        private String company;

        /**
         * 职位
         */
        private String position;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 系统类型
         */
        private String phoneSystem;

        /**
         * 手机类型
         */
        private String phoneType;


        /**
         * 通话次数
         */
        private String callCount;

        /**
         * 最近被联系时间
         */
        @FieldMapping(date = true)
        private String calledTime;

        /**
         * 备注
         */
        private String remark;

        /**
         * 创建时间
         */
        @FieldMapping(date = true)
        private String createTime;

        /**
         * 修改时间
         */
        @FieldMapping(date = true)
        private String modifyTime;
    }

    @Data
    public static class  UploadPhoneCallDetailDTO{
        /**
         *  通话类型
         */
        private String type;
        /**
         *  联系人
         */
        private String name;
        /**
         *  通话号码
         */
        private String number;
        /**
         *  号码类型
         */
        private Integer numberType;
        /**
         *  通话时间
         */
        @FieldMapping(date = true)
        private String date;
        /**
         * 通话时长
         */
        private Integer duration;
    }

    @Data
    public static class  UploadSmsDetailDTO{
        /**
         * 接收人电话
         */
        private String address;

        /**
         * 接收人姓名
         */
        private String person;
        /**
         * 发收时间
         */
        @FieldMapping(date = true)
        private String date;

        /**
         * 短信类型, 1 - 接收到的,2 - 已发出的, 3 - 草稿
         * 1inbox 收到的
         * 2sent 已发出
         * 3draft 草稿
         * 4outbox 发件箱(正在发送)
         * 5failed 发送失败
         * 6queued 队列列中
         */
        @FieldMapping(enumClass = AntiFraudDetailEnum.SmsTypeEnum.class,value = "type")
        private String type;

        /**
         * 是否阅读,0未读,1已读, 只用于 type 为 1 的已接收到的短信
         */
        @FieldMapping(enumClass = AntiFraudDetailEnum.IsReadEnum.class,value = "read")
        private String read;

        /**
         * 发出的短信状态, 只用于 type 为 2 的已发出的短信
         * -1default,无无状态
         * 0已发送成功
         * 64pending
         * 128发送失败
         */
        @FieldMapping(enumClass = AntiFraudDetailEnum.SmsStatusEnum.class,value = "status")
        private String status;

        /**
         * 短信内容
         */
        private String body;
    }

    @Data
    public static class  UploadGpsDetailDTO{
        /**
         * 经度
         */
        private Double longitude;
        /**
         * 纬度
         */
        private Double latitude;

        /**
         * 省份
         */
        private String province;
        /**
         * 城市
         */
        private String city;
        /**
         * 详细地址
         */
        private String address;
        /**
         * 海拔
         */
        private Double altitude;

        private String httpTimestamp;
        /**
         * 采集位置/页面
         */
        private String uploadInfoOpportunity;
        /**
         * 版本号
         */
        private String version;
        /**
         * 是否授权：1-NULL 2-授权 3-已拒绝
         */
        private String isAuthorized;

        /**
         * 设备平台：iso;Android
         */
        private String os;
    }

    @Data
    public static class UploadAppDetailDTO{
        /**
         * 包名
         */
        private String packageName;

        /**
         * app名称
         */
        private String appName;
        /**
         * 最近打开时间
         */
        @FieldMapping(date = true)
        private String lastOpenTime;

        /**
         * 最近更新时间
         */
        @FieldMapping(date = true)
        private String lastUpdateTime;
    }


    @Data
    public static class CustomCallLogRecordDTO{
        /**
         * 联络方向
         */
        private String fromWayName;

        /**
         * 联络方式
         */
        private String fromTypeName;

        /**
         * 创建人机构
         */
        private String crtDeptName;

        /**
         * 创建人名称
         */
        private String crtName;

        /**
         * 服务类别名称
         */
        private String serviceTypeName;

        /**
         * 具体类别名称
         */
        private String itemName;

        /**
         * 三级类别名称
         */
        private String threeTypeName;

        /**
         * 创建时间
         */
        private String crtDt;

        /**
         * 备注
         */
        private String remark;
    }

    @Data
    public static class UploadPhoneInfoDTO {
        /**
         * 设备ID
         */
        private String deviceId;
        /**
         * 电话号码
         */
        @Sensitive(strategy = SensitiveStrategy.PHONE)
        private String phoneNumber;
        /**
         * 设备型号
         */
        private String model;
        /**
         * 是否ROOT
         */
        private String root;
        /**
         * CELLIP地址
         */
        private String cellIp;
        /**
         * 无线IP地址
         */
        private String wifiIp;
        /**
         * 无线Mac地址
         */
        private String wifiMac;
        /**
         * 手机品牌
         */
        private String brand;
        /**
         * 系统类型
         */
        private String os;
        /**
         * 语言
         */
        private String language;
        /**
         * 抓取时间
         */
        private String receiveTime;

    }
}
