package com.weicai.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description: 关系网络主表查询入参
 * Date: 2025/5/22
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class NetworkNodeTagAddDTO {
    /**
     * 节点id
     */
    @NotBlank(message = "节点id不能为空.")
    private String nodeId;

    /**
     * 节点标签code1.团伙；2.疑似团伙；3.清白；4.其他情况； 5. 中介
     */
    @NotBlank(message = "节点标签不能为空.")
    private String tagCode;
    /**
     * 节点code
     */
    private String nodeCode;
}
