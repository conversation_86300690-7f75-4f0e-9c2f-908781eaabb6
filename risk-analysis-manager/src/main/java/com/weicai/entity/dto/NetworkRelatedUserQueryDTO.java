package com.weicai.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description: 关系网络主表查询入参
 * Date: 2025/5/22
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class NetworkRelatedUserQueryDTO {
    /**
     * 节点id
     */
    @NotBlank(message = "节点id不能为空.")
    private String nodeId;

    /**
     * 节点类型
     */
    private String toNodeType;
    /**
     * 每页数量
     */
    private Integer pageSize = 10;
    /**
     * 当前页码
     */
    private Integer pageNum = 1;
}
