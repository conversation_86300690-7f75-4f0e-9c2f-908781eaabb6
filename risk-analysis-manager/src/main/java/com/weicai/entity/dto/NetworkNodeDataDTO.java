package com.weicai.entity.dto;

import lombok.Data;

/**
 *
 * {
 *                     "firstAssociatedTime": "",
 *                     "lastAssociatedTime": "",
 *                     "name": "",
 *                     "nodeCode": "VBB9VKrmIs2xfp3XzzV4iErIhSXBCQu0dT71OPzYNGE=",
 *                     "associatedCount": "",
 *                     "nodeType": "ID_CARD"
 *                 }
 * <AUTHOR>
 * @description: TODO
 * Date: 2025/5/28
 */
@Data
public class NetworkNodeDataDTO {
    private String nodeId;
    private String name;
    private String nodeCode;
    private String associatedCount;
    private String nodeType;
}
