package com.weicai.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description: 关系网络关联节点反欺诈记录查询入参
 * Date: 2025/5/22
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class NetworkRelatedNodeRecordQueryDTO {
    /**
     * fromNodeId节点id
     */
    @NotBlank(message = "来源节点id不能为空.")
    private String relatedNodeId;

    private Integer pageSize = 10;

    private Integer pageNum = 1;

}
