package com.weicai.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 关系网络主表查询入参
 * Date: 2025/5/22
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class NetworkRelatedNodeRecordAddDTO {
    /**
     * 来源节点id
     */
    @NotBlank(message = "来源节点id不能为空.")
    private String fromNodeId;
    /**
     * 来源节点code
     */
    @NotBlank(message = "来源节点code不能为空.")
    private String fromNodeCode;

    /**
     * 关联节点信息
     */
    @NotEmpty(message = "关联节点信息不能为空.")
    private List<NetworkRelatedNodeDTO> relatedNodes;
    /**
     * 拨打模板，0：固话；1：手机
     */
    private String callType;
    /**
     * 拨打状态0. 致电接通；1.致电无人接听；2.致电正在通话；3.致电停机；4.致电关机；5.致电空号；6.致电忙音；7.致电呼叫转移；8.致电接通后挂断
     */
    private String callStatus;
    /**
     * 用户标签1.外部高风险；2.团伙欺诈；3.伪冒用户；4.中介引导（风险较高）；5.中介引导（风险较低）；6.关联欺诈；7.清白
     */
    private String userTag;
    /**
     * 中介标签1.不良中介；2.一般中介；3.非中介
     */
    private String intermediaryTag;
    /**
     * 记录内容
     */
    private String recordContent;

    /**
     *
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class NetworkRelatedNodeDTO{
        /**
         *关联节点id
         */
        private String relatedNodeId;
        /**
         * 关联节点code
         */
        private String relatedNodeCode;
    }

}
