package com.weicai.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 人工审核状态缓存注解
 * 用于自动管理Redis中的审核状态
 * 
 * <AUTHOR>
 * @since 2024/12/26
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AuditStatusCache {
    
    /**
     * 操作类型
     */
    OperationType operation();
    
    /**
     * userKey参数名称，用于从方法参数中获取userKey
     * 支持SpEL表达式，如：#fraudCaseApplyDTO.userKey 或 #userKey
     */
    String userKeyExpression() default "#userKey";
    
    /**
     * 缓存过期时间（秒），默认7天
     */
    long expireSeconds() default 7 * 24 * 60 * 60;
    
    /**
     * 操作类型枚举
     */
    enum OperationType {
        /**
         * 进入审核状态
         */
        ENTER_AUDIT,
        
        /**
         * 退出审核状态
         */
        EXIT_AUDIT
    }
}
