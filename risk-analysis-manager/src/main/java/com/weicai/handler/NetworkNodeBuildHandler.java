package com.weicai.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.weicai.client.DwClient;
import com.weicai.entity.dto.*;
import com.weicai.entity.vo.NetworkNodeVO;
import com.weicai.enumeration.NetworkNodeTypeEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: TODO
 * Date: 2025/5/28
 */
@Component
@RefreshScope
public class NetworkNodeBuildHandler {
    @Resource
    private DwClient dwClient;
    @Value(value = "${network.node.query.lazy.maxNodeCount}")
    private Integer maxNodeCount;
    @Value("${network.node.query.lazy.weightCount}")
    private Integer weightCount;

    public NetworkNodeVO execute(NetworkNodeQueryDTO param) {
        if (StrUtil.isBlank(param.getMaxHops())) {
            //默认1跳
            param.setMaxHops("1");
        }
        NetworkNodeVO result = new NetworkNodeVO();
        if (param.getLazyTag() == 1) {
            result.setLazyTag(1);
        }
        NetworkNodeCountDTO networkNodeCountDTO = dwClient.queryNodeNetworkCount(param);
        //小于最大节点限制时候直接查数据,+1是有根节点在list中
        if (ObjectUtil.isNull(networkNodeCountDTO.getTotal()) || networkNodeCountDTO.getTotal() == 0) {
            return result;
        } else if (networkNodeCountDTO.getTotal() <= (maxNodeCount + 1)) {
            result = dwClient.queryNodeNetwork(param);
        } else {
            // 懒加载,只查一跳数据
            param.setMaxHops("1");
            networkNodeCountDTO = dwClient.queryNodeNetworkCount(param);
            result = dwClient.queryNodeNetwork(param);
            List<NetworkNodeDTO> nodes = result.getNodes();
            List<NetworkNodeLineDTO> lines = result.getLines();
            System.out.println(JSONUtil.toJsonStr(lines));
            nodes.forEach(item -> {
                item.getData().setNodeId(item.getId());
            });
            List<NetworkNodeDataDTO> nodeData = nodes.stream().map(NetworkNodeDTO::getData).collect(Collectors.toList());
            if (networkNodeCountDTO.getTotal() > (maxNodeCount + 1)) {
                weightLazyQuery(result, lines, nodeData);
            }
            if (0 == param.getLazyTag()) {
                result.setLazyTag(1);
            }
        }
        if ((ObjectUtil.isNotNull(param.getLazyTag()) && param.getLazyTag() == 1) || (ObjectUtil.isNotNull(result.getLazyTag()) && result.getLazyTag() == 1)) {
            result.setLazyTag(1);
            String rootNodeId = result.getRootId();
            List<NetworkNodeDTO> nodes = result.getNodes();
            if (CollUtil.isEmpty(nodes)) {
                return result;
            }
            //懒加载过程中,查询子节点的关联节点数
            nodes.forEach(item -> {
                item.getData().setNodeId(item.getId());
            });
            List<NetworkNodeDataDTO> nodeData = nodes.stream().map(NetworkNodeDTO::getData).collect(Collectors.toList());
            for (NetworkNodeDataDTO node : nodeData) {
                if (rootNodeId.equals(node.getNodeId())) {
                    node.setAssociatedCount(null);
                } else {
                    NetworkNodeCountDTO countDTO = dwClient.queryNodeNetworkCount(NetworkNodeQueryDTO.builder().nodeCode(node.getNodeId()).nodeType(node.getNodeType()).maxHops("1").build());
                    //需要去除当前节点和根节点两个已有节点关系
                    if (countDTO.getTotal() > 0) {
                        node.setAssociatedCount(String.valueOf(Math.max(countDTO.getTotal() - 1, 0)));
                    } else {
                        node.setAssociatedCount(String.valueOf(0));
                    }
                }
            }
        } else {
            result.setLazyTag(0);
        }
        return result;
    }

    /**
     * 带权重懒加载查询
     *
     * @param result
     * @param lines
     * @param nodeData
     */
    private void weightLazyQuery(NetworkNodeVO result, List<NetworkNodeLineDTO> lines, List<NetworkNodeDataDTO> nodeData) {
        //获得根节点
        String rootNodeId = result.getRootId();
        NetworkNodeDataDTO rootNode = nodeData.stream().filter(item -> rootNodeId.equals(item.getNodeId())).findFirst().get();
        rootNode.setNodeId(rootNodeId);
//        nodeData.remove(rootNode);
        //排除nodeData中的根节点
        nodeData.removeIf(obj -> rootNodeId.equals(obj.getNodeId()));
        //一跳数据大于maxNodeSize 权重懒加载
        List<NetworkNodeDataDTO> nodeDataList = new ArrayList<>();
        List<NetworkNodeLineDTO> lineList = new ArrayList<>();
        Map<String, List<NetworkNodeDataDTO>> listMap = nodeData.stream().collect(Collectors.groupingBy(obj -> obj.getNodeType(), Collectors.mapping(obj -> obj, Collectors.toList())));
        String userCode = NetworkNodeTypeEnum.USER.getCode();
        for (String type : listMap.keySet()) {
            List<NetworkNodeDataDTO> list1 = listMap.get(type);
            List<String> nodeIds = list1.stream().map(NetworkNodeDataDTO::getNodeId).collect(Collectors.toList());
            if (list1.size() > weightCount) {
                //筛选关联时间前十节点
                //每个节点对应最大创建时间 type 为user选用from线,其他类型选用to线
                List<NetworkNodeLineDTO> typeLines = lines.stream().filter(item -> {
                    return nodeIds.contains(userCode.equals(type) ? item.getFrom() : item.getTo());
                }).collect(Collectors.toList());
                //获取每个关联节点的创造时间最大线对应的点
                Map<String, String> collect2 = typeLines.stream().collect(Collectors.toMap(userCode.equals(type) ? NetworkNodeLineDTO::getFrom : NetworkNodeLineDTO::getTo, NetworkNodeLineDTO::getCreate_time, (v1, v2) -> {
                    if (v1.compareTo(v2) > 0) {
                        return v1;
                    } else {
                        return v2;
                    }
                }));
                // 将Map的条目转换为列表并排序（按值降序）
                List<Map.Entry<String, String>> sortedEntries = new ArrayList<>(collect2.entrySet());
                sortedEntries.sort(Collections.reverseOrder(Map.Entry.comparingByValue()));
                // 按照权重进行截取元素
                List<Map.Entry<String, String>> top = sortedEntries.subList(0, Math.min(sortedEntries.size(), weightCount));
                List<String> tempNodeIds = top.stream().map(item -> item.getKey()).collect(Collectors.toList());

                List<NetworkNodeLineDTO> collect3 = lines.stream().filter(item -> {
                            String lineNodeId = userCode.equals(type) ? item.getFrom() : item.getTo();
                            return tempNodeIds.contains(lineNodeId);
                        }
                ).collect(Collectors.toList());
                List<NetworkNodeDataDTO> collect4 = list1.stream().filter(item -> tempNodeIds.contains(item.getNodeId())).collect(Collectors.toList());
                nodeDataList.addAll(collect4);
                lineList.addAll(collect3);
            } else {
                //获取连线
                List<NetworkNodeLineDTO> collect1 = lines.stream().filter(item -> {
                            //所有连接线都获取
                            return nodeIds.contains(item.getTo()) || nodeIds.contains(item.getFrom());
                        }
                ).collect(Collectors.toList());
                lineList.addAll(collect1);
                nodeDataList.addAll(list1);
            }
        }
        //组装node
        List<NetworkNodeDTO> newNodes = new ArrayList<>();
        nodeDataList.forEach(item -> {
//            NetworkNodeCountDTO countDTO = dwClient.queryNodeNetworkCount(NetworkNodeQueryDTO.builder().nodeCode(item.getNodeId()).nodeType(item.getNodeType()).maxHops("1").build());
//            item.setAssociatedCount(String.valueOf(countDTO.getTotal() - 2));
            newNodes.add(NetworkNodeDTO.builder().id(item.getNodeId()).data(item).build());
        });
        //加入根节点
//        NetworkNodeCountDTO countDTO = dwClient.queryNodeNetworkCount(NetworkNodeQueryDTO.builder().nodeCode(rootNodeId).nodeType(rootNode.getNodeType()).maxHops("1").build());
        rootNode.setAssociatedCount(null);
        rootNode.setNodeId(rootNodeId);
        newNodes.add(NetworkNodeDTO.builder().id(rootNodeId).data(rootNode).build());
        result.setNodes(newNodes);
        result.setLines(lineList);
    }
}
