package com.weicai.controller;

import com.weicai.entity.JsonResult;
import com.weicai.entity.dto.ManualAuditStatusQueryDTO;
import com.weicai.entity.vo.ManualAuditStatusVO;
import com.weicai.service.ManualAuditStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 人工审核状态查询接口
 * 
 * <AUTHOR>
 * @since 2024/12/26
 */
@RestController
@RequestMapping("/manualAuditStatus")
@Slf4j
public class ManualAuditStatusController {
    
    @Resource
    private ManualAuditStatusService manualAuditStatusService;
    
    /**
     * 查询用户是否正在人工审核中
     * 
     * @param queryDTO 查询请求
     * @return 审核状态信息
     */
    @PostMapping("/query")
    public JsonResult<ManualAuditStatusVO> queryManualAuditStatus(@RequestBody @Valid ManualAuditStatusQueryDTO queryDTO) {
        log.info("查询用户审核状态，userKey: {}", queryDTO.getUserKey());
        
        try {
            boolean isInManualAudit = manualAuditStatusService.isInManualAudit(queryDTO.getUserKey());
            ManualAuditStatusVO result = ManualAuditStatusVO.success(queryDTO.getUserKey(), isInManualAudit);
            
            log.info("用户 {} 审核状态查询完成，结果: {}", queryDTO.getUserKey(), 
                    isInManualAudit ? "审核中" : "未审核");
            
            return JsonResult.success(result);
            
        } catch (Exception e) {
            log.error("查询用户 {} 审核状态失败", queryDTO.getUserKey(), e);
            return JsonResult.error("查询审核状态失败");
        }
    }
    
    /**
     * 简化版查询接口，直接返回布尔值
     * 
     * @param queryDTO 查询请求
     * @return 是否正在审核中
     */
    @PostMapping("/isInAudit")
    public JsonResult<Boolean> isInManualAudit(@RequestBody @Valid ManualAuditStatusQueryDTO queryDTO) {
        log.info("简化查询用户审核状态，userKey: {}", queryDTO.getUserKey());
        
        try {
            boolean isInManualAudit = manualAuditStatusService.isInManualAudit(queryDTO.getUserKey());
            
            log.info("用户 {} 审核状态查询完成，结果: {}", queryDTO.getUserKey(), 
                    isInManualAudit ? "审核中" : "未审核");
            
            return JsonResult.success(isInManualAudit);
            
        } catch (Exception e) {
            log.error("查询用户 {} 审核状态失败", queryDTO.getUserKey(), e);
            return JsonResult.error("查询审核状态失败");
        }
    }
}
