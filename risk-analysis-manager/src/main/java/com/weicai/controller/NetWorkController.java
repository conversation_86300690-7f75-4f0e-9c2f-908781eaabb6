package com.weicai.controller;

import com.weicai.entity.JsonResult;
import com.weicai.entity.dto.*;
import com.weicai.entity.po.RaNetworkNodeTagDO;
import com.weicai.entity.po.RaNetworkRelatedNodeRecordDO;
import com.weicai.entity.vo.NetworkNodeTagListVO;
import com.weicai.entity.vo.NetworkNodeVO;
import com.weicai.entity.vo.NetworkRelatedUserPageVO;
import com.weicai.query.PageInfo;
import com.weicai.service.NetworkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description: 关系网络controller
 * Date: 2025/5/22
 */
@RestController
@RequestMapping("/network")
@Slf4j
public class NetWorkController {
    @Resource
    private NetworkService networkService;

    @PostMapping("/node/query")
    public JsonResult<NetworkNodeVO> queryNodeNetwork(@Valid @RequestBody NetworkNodeQueryDTO param) {
        return JsonResult.success(networkService.queryNodeNetwork(param));
    }

    /**
     * 节点关联用户查询
     * /network/relatedNode/pageList
     *
     * @param param
     * @return
     */
    @PostMapping("/relatedNode/pageList")
    public JsonResult<PageInfo<NetworkRelatedUserPageVO>> queryRelatedNodePageList(@Valid @RequestBody NetworkRelatedUserQueryDTO param) {
        return JsonResult.success(networkService.queryRelatedNodePageList(param));
    }

    /**
     * 节点标签新增
     * /network/node/tag/add
     *
     * @param param
     * @return
     */
    @PostMapping("/node/tag/add")
    public JsonResult<Void> addNodeTag(@Valid @RequestBody NetworkNodeTagAddDTO param) {
        networkService.addNodeTag(param);
        return JsonResult.success();
    }

    /**
     * 节点标签查询
     * /network/node/tag/add
     *
     * @param param
     * @return
     */
    @PostMapping("/node/tag/pageList")
    public JsonResult<PageInfo<NetworkNodeTagListVO>> queryNodeTagList(@Valid @RequestBody NetworkNodeTagQueryDTO param) {
        return JsonResult.success(networkService.queryNodeTagList(param));
    }

    /**
     * 最新节点标签查询
     * /network/node/tag/add
     *
     * @param param
     * @return
     */
    @PostMapping("/node/tag/lastOne")
    public JsonResult<RaNetworkNodeTagDO> queryNodeTagLastOne(@Valid @RequestBody NetworkNodeTagQueryDTO param) {
        return JsonResult.success(networkService.queryNodeTagLastOne(param));
    }

    /**
     * 节点标签新增
     * /network/node/tag/add
     *
     * @param param
     * @return
     */
    @PostMapping("/relatedNode/record/add")
    public JsonResult<Void> addRelatedNodeRecord(@Valid @RequestBody NetworkRelatedNodeRecordAddDTO param) {
        networkService.addRelatedNodeRecord(param);
        return JsonResult.success();
    }

    /**
     * 节点标签查询
     * /network/node/tag/add
     *
     * @param param
     * @return
     */
    @PostMapping("/relatedNode/record/pageList")
    public JsonResult<PageInfo<RaNetworkRelatedNodeRecordDO>> queryRelatedNodeRecordPageList(@Valid @RequestBody NetworkRelatedNodeRecordQueryDTO param) {
        return JsonResult.success(networkService.queryRelatedNodeRecordPageList(param));
    }
}
