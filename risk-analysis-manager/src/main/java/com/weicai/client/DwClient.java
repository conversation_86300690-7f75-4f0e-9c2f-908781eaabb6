package com.weicai.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.weicai.entity.JsonResult;
import com.weicai.entity.dto.NetworkNodeCountDTO;
import com.weicai.entity.dto.NetworkNodeQueryDTO;
import com.weicai.entity.dto.NetworkRelatedUserQueryDTO;
import com.weicai.entity.vo.NetworkNodeVO;
import com.weicai.entity.vo.NetworkRelatedUserPageVO;
import com.weicai.exception.BusinessException;
import com.weicai.query.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.net.URI;
import java.util.concurrent.TimeUnit;

/**
 * 数仓client
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DwClient {

    @Value("${dw.host}")
    private String dwUrl;

    @Resource
    private RestTemplate restTemplate;

    public NetworkNodeVO queryNodeNetwork(NetworkNodeQueryDTO param) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        String url = dwUrl + "/api/dwc/autifraud/networking";
        JsonResult<NetworkNodeVO> resp = null;
        try {
            // 构建URL
            URI uri = UriComponentsBuilder.fromHttpUrl(url)
                    .queryParam("nodeType", param.getNodeType())
                    .queryParam("nodeCode", param.getNodeCode())
                    .queryParam("maxHops", param.getMaxHops())
                    .build()
                    .toUri();
            resp = restTemplate.getForObject(uri, JsonResult.class);
        } catch (Exception e) {
            String errorMsg = String.format("call dw exception, msg: %s .", e.getMessage());
            log.error(errorMsg + "url: {}, params: {}, ", url, JSON.toJSONString(param));
            throw new BusinessException("图数据库接口请求超时");
        }
        if (ObjectUtil.isNull(resp) || ObjectUtil.isNull(resp.getCode()) || resp.getCode() != 1) {
            String errorMsg = String.format("call dw error, result: %s", JSON.toJSONString(resp));
            log.error(errorMsg + "url: {}, params: {}, ", url, JSON.toJSONString(resp));
            throw new BusinessException(errorMsg);
        }
        long elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("call dw succeed, url:{},param:{},response:{},costTime:{}", url, JSON.toJSONString(param), StringUtils.abbreviate(JSON.toJSONString(resp.getData()), 1000), elapsed);
        return JSONUtil.toBean(JSONUtil.parseObj(resp.getData()), NetworkNodeVO.class);
    }

    public PageInfo<NetworkRelatedUserPageVO> queryRelatedUser(NetworkRelatedUserQueryDTO param) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        String url = dwUrl + "/api/dwc/autifraud/related_user";
        JsonResult<NetworkRelatedUserPageVO> resp = null;
        try {
            // 构建URL
            URI uri = UriComponentsBuilder.fromHttpUrl(url)
                    .queryParam("fromNodeId", param.getNodeId())
//                    .queryParam("toNodeType","USER")
                    .queryParam("pageSize", param.getPageSize())
                    .queryParam("pageNum", param.getPageNum())
                    .build()
                    .toUri();
            resp = restTemplate.getForObject(uri, JsonResult.class);
        } catch (Exception e) {
            String errorMsg = String.format("call dw exception, msg: %s .", e.getMessage());
            log.error(errorMsg + "url: {}, params: {}, ", url, JSON.toJSONString(param));
            throw new BusinessException("图数据库接口请求超时");
        }
        if (ObjectUtil.isNull(resp) || ObjectUtil.isNull(resp.getCode()) || resp.getCode() != 1) {
            String errorMsg = String.format("call dw error, result: %s", JSON.toJSONString(resp));
            log.error(errorMsg + "url: {}, params: {}, ", url, JSON.toJSONString(resp));
            throw new BusinessException(errorMsg);
        }
        long elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("call dw succeed, url:{},param:{},response:{},costTime:{}", url, JSON.toJSONString(param), StringUtils.abbreviate(JSON.toJSONString(resp.getData()), 1000), elapsed);
        JSONObject jsonObject = JSONUtil.parseObj(resp.getData());
        PageInfo<NetworkRelatedUserPageVO> result = new PageInfo<>();
        result.setPageNum(param.getPageNum());
        result.setPageSize(param.getPageSize());
        result.setTotal(jsonObject.getLong("total"));
        result.setPages(jsonObject.getInt("totalPage"));
        if (CollectionUtil.isNotEmpty(jsonObject.getJSONArray("list"))) {
            result.setList(JSONUtil.toList(jsonObject.getJSONArray("list"), NetworkRelatedUserPageVO.class));
        }
        return result;
    }

    /**
     * {
     *         "userNum": 14,
     *         "total": 359,
     *         "rootId": "a212c1d273fb4a65e53ce8696d615234"
     *     }
     * @param param
     * @return
     */
    public NetworkNodeCountDTO queryNodeNetworkCount(NetworkNodeQueryDTO param) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        String url = dwUrl + "/api/dwc/autifraud/statistic";
        JsonResult<NetworkNodeVO> resp = null;
        try {
            // 构建URL
            URI uri = UriComponentsBuilder.fromHttpUrl(url)
                    .queryParam("nodeType", param.getNodeType())
                    .queryParam("nodeCode", param.getNodeCode())
                    .queryParam("maxHops", param.getMaxHops())
                    .build()
                    .toUri();
            resp = restTemplate.getForObject(uri, JsonResult.class);
        } catch (Exception e) {
            String errorMsg = String.format("call dw exception, msg: %s .", e.getMessage());
            log.error(errorMsg + "url: {}, params: {}, ", url, JSON.toJSONString(param));
            throw new BusinessException("图数据库接口请求超时");
        }
        if (ObjectUtil.isNull(resp) || ObjectUtil.isNull(resp.getCode()) || resp.getCode() != 1) {
            String errorMsg = String.format("call dw error, result: %s", JSON.toJSONString(resp));
            log.error(errorMsg + "url: {}, params: {}, ", url, JSON.toJSONString(resp));
            throw new BusinessException(errorMsg);
        }
        long elapsed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("call dw succeed, url:{},param:{},response:{},costTime:{}", url, JSON.toJSONString(param), StringUtils.abbreviate(JSON.toJSONString(resp.getData()), 1000), elapsed);
        return JSONUtil.toBean(JSONUtil.parseObj(resp.getData()), NetworkNodeCountDTO.class);
    }

    public static void main(String[] args) {
        String dwUrl = "https://vulcan.weicai.com.cn";
        String url = dwUrl + "/api/dwc/autifraud/networking";
        NetworkNodeQueryDTO param = new NetworkNodeQueryDTO();
        param.setNodeType("USER");
        param.setNodeCode("00005a46cf405ad44c2cfdc476a13277");
        param.setMaxHops("2");
        param.setNeedDecrypt(0);
        HttpRequest get = HttpUtil.createGet(url);
        get.form("nodeType", "USER")
                .form("nodeCode", "00005a46cf405ad44c2cfdc476a13277")
                .form("maxHops", 2);
//            get.header()
        System.out.println(get.getUrl());
        HttpResponse execute = get.execute();
        System.out.println(execute.body());
    }
}
