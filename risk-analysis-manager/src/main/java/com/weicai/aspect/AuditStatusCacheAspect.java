package com.weicai.aspect;

import com.weicai.annotation.AuditStatusCache;
import com.weicai.constants.RedisKeyConstants;
import com.weicai.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * 人工审核状态缓存切面
 * 
 * <AUTHOR>
 * @since 2024/12/26
 */
@Aspect
@Component
@Slf4j
public class AuditStatusCacheAspect {
    
    @Resource
    private RedisService redisService;
    
    private final ExpressionParser parser = new SpelExpressionParser();
    private final DefaultParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();
    
    /**
     * 在方法成功执行后更新审核状态缓存
     */
    @AfterReturning("@annotation(auditStatusCache)")
    public void updateAuditStatusCache(JoinPoint joinPoint, AuditStatusCache auditStatusCache) {
        try {
            // 获取userKey
            String userKey = getUserKey(joinPoint, auditStatusCache.userKeyExpression());
            if (userKey == null || userKey.trim().isEmpty()) {
                log.warn("无法获取userKey，跳过审核状态缓存更新");
                return;
            }
            
            String redisKey = RedisKeyConstants.MANUAL_AUDIT_STATUS + userKey;
            
            // 根据操作类型更新缓存
            if (auditStatusCache.operation() == AuditStatusCache.OperationType.ENTER_AUDIT) {
                // 进入审核状态，设置缓存值为"1"
                redisService.setEx(redisKey, "1", auditStatusCache.expireSeconds(), TimeUnit.SECONDS);
                log.info("用户 {} 进入人工审核状态，已更新Redis缓存", userKey);
            } else if (auditStatusCache.operation() == AuditStatusCache.OperationType.EXIT_AUDIT) {
                // 退出审核状态，删除缓存
                redisService.delete(redisKey);
                log.info("用户 {} 退出人工审核状态，已删除Redis缓存", userKey);
            }
            
        } catch (Exception e) {
            log.error("更新审核状态缓存失败", e);
        }
    }
    
    /**
     * 从方法参数中获取userKey
     */
    private String getUserKey(JoinPoint joinPoint, String userKeyExpression) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Object[] args = joinPoint.getArgs();
            String[] paramNames = nameDiscoverer.getParameterNames(method);
            
            // 创建SpEL上下文
            EvaluationContext context = new StandardEvaluationContext();
            
            // 将方法参数添加到上下文中
            if (paramNames != null) {
                for (int i = 0; i < paramNames.length; i++) {
                    context.setVariable(paramNames[i], args[i]);
                }
            }
            
            // 解析表达式
            Expression expression = parser.parseExpression(userKeyExpression);
            Object result = expression.getValue(context);
            
            return result != null ? result.toString() : null;
            
        } catch (Exception e) {
            log.error("解析userKey表达式失败: {}", userKeyExpression, e);
            return null;
        }
    }
}
