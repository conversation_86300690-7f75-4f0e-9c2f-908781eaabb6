package com.weicai.enumeration;


public class AntiFraudDetailEnum {
    /**
     * 是否授权
     */
    public enum IsAuthorizedEnum {
        NULl(1, ""),
        AUTHORIZED(2, "授权"),
        REFUSE(3, "已拒绝");

        private int value;
        private String desc;
        public int value() {
            return value;
        }
        public String desc(){
            return desc;
        }
        IsAuthorizedEnum(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        /**
         * 获取枚举描述值
         * @param param
         * @return
         */
        public static Object getValue(Object param){
            IsAuthorizedEnum[] enums = IsAuthorizedEnum.values();
            for (IsAuthorizedEnum enumElem : enums){
                if (enumElem.value == (Integer) param){
                    return enumElem.desc;
                }
            }
            return null;
        }
    }


    public enum IsReadEnum {
        /**
         * 是否已读
         */
        READ(1, "已读"),
        NO_READ(0, "未读");

        private int value;
        private String desc;
        public int value() {
            return value;
        }
        public String desc(){
            return desc;
        }
        IsReadEnum(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
        /**
         * 获取枚举描述值
         * @param param
         * @return
         */
        public Object getValue(Object param){
            IsReadEnum[] enums = IsReadEnum.values();
            for (IsReadEnum enumElem : enums){
                if (enumElem.value == (Integer) param){
                    return enumElem.desc;
                }
            }
            return null;
        }
    }


    public enum SmsTypeEnum {
        /**
         * 是否已读
         */
        INBOX(1, "收到"),
        SENT(2, "已发出"),
        DRAFT(3, "草稿"),
        OUTBOX(4, "发件箱"),
        FAILED(5, "发送失败"),
        Queued(6, "队列");

        private int value;
        private String desc;
        public int value() {
            return value;
        }
        public String desc(){
            return desc;
        }
        SmsTypeEnum(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
        /**
         * 获取枚举描述值
         * @param param
         * @return
         */
        public Object getValue(Object param){
            SmsTypeEnum[] enums = SmsTypeEnum.values();
            for (SmsTypeEnum enumElem : enums){
                if (enumElem.value == (Integer) param){
                    return enumElem.desc;
                }
            }
            return null;
        }
    }


    public enum SmsStatusEnum {
        /**
         * 短信状态
         */
        C_G_J_S(0, "成功接收"),
        D_F_S(1, "待发送"),
        F_S_Z(2, "发送中"),
        Y_F_S(3, "已发送"),
        F_S_S_B(4,"发送失败"),
        B_L(5,"保留"),
        Y_S_C(6,"已删除"),
        NO_S(-1,"没有状态信息,尚未收到关于消息状态的信息");

        private int value;
        private String desc;
        public int value() {
            return value;
        }
        public String desc(){
            return desc;
        }
        SmsStatusEnum(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
        /**
         * 获取枚举描述值
         * @param param
         * @return
         */
        public Object getValue(Object param){
            SmsStatusEnum[] enums = SmsStatusEnum.values();
            for (SmsStatusEnum enumElem : enums){
                if (enumElem.value == (Integer) param){
                    return enumElem.desc;
                }
            }
            return null;
        }
    }



}
