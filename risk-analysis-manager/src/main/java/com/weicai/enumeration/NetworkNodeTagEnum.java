package com.weicai.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 关系网络节点类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum NetworkNodeTagEnum {
    GANG("1", "团伙"),
    SUSPECTED_GANG("2", "疑似团伙"),
    INNOCENCE("3", "清白"),
    OTHER("4", "其他情况"),
    INTERMEDIARY("5", "中介"),
    ;
    private final String code;
    private final String desc;

    static Map<String, String> map = new HashMap<>(25);

    static {
        Arrays.stream(NetworkNodeTagEnum.values()).forEach(item -> {
            map.put(item.code, item.desc);
        });
    }

    public static Optional<NetworkNodeTagEnum> getByCode(String code) {
        for (NetworkNodeTagEnum item : values()) {
            if (item.code.equals(code)) {
                return Optional.of(item);
            }
        }
        return Optional.empty();
    }

    public static String getDecByCode(String code) {
        return map.get(code);
    }


}
