package com.weicai.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 关系网络节点类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum NetworkNodeTypeEnum {
    ID_CARD("ID_CARD", "身份证号", true),
    PHONE("PHONE", "手机号", true),
    DEVICE("DEVICE", "设备类型", true),
    ADDRESS("ADDRESS", "地址", false),
    GPS("GPS", "gps", false),
    USER("USER", "userKey", false),
    BANK_CARD("BANK_CARD", "银行卡号", true),
    ;
    private final String code;
    private final String desc;
    private final boolean needDecrypt;

    static Map<String, Boolean> map = new HashMap<>(25);

    static {
        Arrays.stream(NetworkNodeTypeEnum.values()).forEach(item -> {
            map.put(item.code, item.needDecrypt);
        });
    }

    public static Optional<NetworkNodeTypeEnum> getByCode(String code) {
        for (NetworkNodeTypeEnum item : values()) {
            if (item.code.equals(code)) {
                return Optional.of(item);
            }
        }
        return Optional.empty();
    }

    public static Boolean getNeedDecryptByCode(String code) {
        return map.get(code);
    }


}
