package com.weicai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weicai.entity.po.InquiryRecordDO;
import com.weicai.entity.vo.InquiryRecordVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/20 16:45
 */
public interface InquiryRecordService extends IService<InquiryRecordDO> {
    List<InquiryRecordDO> selectByLoanKeys(List<String> loanKeys);

    List<InquiryRecordVO> selectByLoanKey(String loanKey);

    InquiryRecordDO selectFinalOneByUserKey(String userKey);
}
