package com.weicai.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weicai.entity.dto.NetworkNodeTagQueryDTO;
import com.weicai.entity.po.RaNetworkNodeTagDO;
import com.weicai.entity.vo.NetworkNodeTagListVO;
import com.weicai.mapper.di.RaNetworkNodeTagMapper;
import com.weicai.query.PageInfo;
import com.weicai.service.NetworkNodeTagService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * Date: 2025/5/26
 */
@Service
public class NetworkNodeTagServiceImpl extends ServiceImpl<RaNetworkNodeTagMapper, RaNetworkNodeTagDO> implements NetworkNodeTagService {
    @Override
    public PageInfo<NetworkNodeTagListVO> queryNodeTagList(NetworkNodeTagQueryDTO param) {
        LambdaQueryWrapper<RaNetworkNodeTagDO> wrapper = new LambdaQueryWrapper<RaNetworkNodeTagDO>()
                .eq(RaNetworkNodeTagDO::getNodeId, param.getNodeId())
                .eq(RaNetworkNodeTagDO::getDelFlag, 0)
                .orderByDesc(RaNetworkNodeTagDO::getUpdateTime);
        IPage<RaNetworkNodeTagDO> page = new Page<>(param.getPageNum(), param.getPageSize());
        IPage<RaNetworkNodeTagDO> pageList = baseMapper.selectPage(page, wrapper);
        PageInfo<NetworkNodeTagListVO> result = new PageInfo<>();
        result.setPageNum((int) pageList.getCurrent());
        result.setPageSize((int) pageList.getSize());
        result.setTotal(pageList.getTotal());
        if (CollectionUtil.isNotEmpty(pageList.getRecords())) {
            List<NetworkNodeTagListVO> tagListVOS = JSONUtil.toList(JSONUtil.parseArray(pageList.getRecords()), NetworkNodeTagListVO.class);
            result.setList(tagListVOS);
        }
        result.setPages((int) pageList.getPages());
        return result;
    }

    @Override
    public RaNetworkNodeTagDO queryNodeTagLastOne(NetworkNodeTagQueryDTO param) {
        LambdaQueryWrapper<RaNetworkNodeTagDO> wrapper = new LambdaQueryWrapper<RaNetworkNodeTagDO>()
                .eq(RaNetworkNodeTagDO::getNodeId, param.getNodeId())
                .eq(RaNetworkNodeTagDO::getDelFlag, 0)
                .orderByDesc(RaNetworkNodeTagDO::getUpdateTime)
                .last("LIMIT 1");
        return getOne(wrapper);
    }
}
