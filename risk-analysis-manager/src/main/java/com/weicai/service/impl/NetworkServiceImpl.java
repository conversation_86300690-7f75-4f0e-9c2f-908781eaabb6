package com.weicai.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.weicai.caesar.CaesarUtil;
import com.weicai.client.DwClient;
import com.weicai.entity.dto.*;
import com.weicai.entity.po.InquiryRecordDO;
import com.weicai.entity.po.RaNetworkNodeTagDO;
import com.weicai.entity.po.RaNetworkRelatedNodeRecordDO;
import com.weicai.entity.vo.NetworkNodeTagListVO;
import com.weicai.entity.vo.NetworkNodeVO;
import com.weicai.entity.vo.NetworkRelatedUserPageVO;
import com.weicai.enumeration.NetworkNodeTagEnum;
import com.weicai.enumeration.NetworkNodeTypeEnum;
import com.weicai.exception.BusinessException;
import com.weicai.handler.NetworkNodeBuildHandler;
import com.weicai.query.PageInfo;
import com.weicai.service.InquiryRecordService;
import com.weicai.service.NetworkNodeTagService;
import com.weicai.service.NetworkRelateNodeRecordService;
import com.weicai.service.NetworkService;
import com.weicai.utils.ServletUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * Date: 2025/5/22
 */
@Slf4j
@Service
public class NetworkServiceImpl implements NetworkService {
    @Resource
    private DwClient dwClient;
    @Resource
    private NetworkNodeTagService networkNodeTagService;
    @Resource
    private NetworkRelateNodeRecordService networkRelateNodeRecordService;
    @Resource
    private NetworkNodeBuildHandler networkNodeBuildHandler;
    @Resource
    private InquiryRecordService inquiryRecordService;

    @Override
    public NetworkNodeVO queryNodeNetwork(NetworkNodeQueryDTO param) {
        if (!NetworkNodeTypeEnum.getByCode(param.getNodeType()).isPresent()) {
            throw new BusinessException("节点类型不正确.");
        }
        NetworkNodeTypeEnum typeEnum = NetworkNodeTypeEnum.getByCode(param.getNodeType()).get();
        if (1 == (param.getNeedDecrypt()) && NetworkNodeTypeEnum.getNeedDecryptByCode(param.getNodeType())) {
            String encode = null;
            if (NetworkNodeTypeEnum.DEVICE.getCode().equals(param.getNodeType())) {
                // 设备类型，device,android,imei,wifimac
                // md5(concat(device_type, device_id)) as id
                String deviceType = StrUtil.isNotBlank(param.getDeviceType()) ? param.getDeviceType() : "device";
                String device = deviceType + param.getNodeCode();
                encode = StringUtils.lowerCase(SecureUtil.md5(device));
            } else{
                //三要素加密
                encode = StringUtils.upperCase(SecureUtil.md5(param.getNodeCode()));
            }
            param.setNodeCode(encode);
        }
        return networkNodeBuildHandler.execute(param);
    }

    @Override
    public PageInfo<NetworkRelatedUserPageVO> queryRelatedNodePageList(NetworkRelatedUserQueryDTO param) {
        PageInfo<NetworkRelatedUserPageVO> pageInfo = dwClient.queryRelatedUser(param);
        List<NetworkRelatedUserPageVO> list = pageInfo.getList();
        buildRelatedNodePageList(list);
        return pageInfo;
    }

    @Override
    public void addNodeTag(NetworkNodeTagAddDTO param) {
        RaNetworkNodeTagDO entity = buildNodeTagEntity(param);
        networkNodeTagService.save(entity);
    }

    private RaNetworkNodeTagDO buildNodeTagEntity(NetworkNodeTagAddDTO param) {
        RaNetworkNodeTagDO entity = new RaNetworkNodeTagDO();
        entity.setSourceSystem("HAO_HUAN");
        entity.setNodeId(param.getNodeId());
        entity.setNodeCode(param.getNodeCode());
        entity.setTagCode(param.getTagCode());
        entity.setTagDesc(NetworkNodeTagEnum.getDecByCode(param.getTagCode()));
        entity.setRemark("");
        entity.setCreateBy(ServletUtils.getUserName());
        entity.setUpdateBy(ServletUtils.getUserName());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setDelFlag(0);
        return entity;
    }

    @Override
    public PageInfo<NetworkNodeTagListVO> queryNodeTagList(NetworkNodeTagQueryDTO param) {
        return networkNodeTagService.queryNodeTagList(param);
    }

    @Override
    public PageInfo<RaNetworkRelatedNodeRecordDO> queryRelatedNodeRecordPageList(NetworkRelatedNodeRecordQueryDTO param) {
        return networkRelateNodeRecordService.queryRelatedNodeRecordPageList(param);
    }

    @Override
    public void addRelatedNodeRecord(NetworkRelatedNodeRecordAddDTO param) {
        List<RaNetworkRelatedNodeRecordDO> list = buildRelatedNodeRecordEntity(param);
        networkRelateNodeRecordService.saveBatch(list);
    }

    @Override
    public RaNetworkNodeTagDO queryNodeTagLastOne(NetworkNodeTagQueryDTO param) {
        return networkNodeTagService.queryNodeTagLastOne(param);
    }

    private List<RaNetworkRelatedNodeRecordDO> buildRelatedNodeRecordEntity(NetworkRelatedNodeRecordAddDTO param) {
        List<RaNetworkRelatedNodeRecordDO> list = new ArrayList<>();
        for (NetworkRelatedNodeRecordAddDTO.NetworkRelatedNodeDTO relatedNode : param.getRelatedNodes()) {
            RaNetworkRelatedNodeRecordDO entity = new RaNetworkRelatedNodeRecordDO();
            entity.setSourceSystem("HAO_HUAN");
            entity.setFromNodeId(param.getFromNodeId());
            entity.setFromNodeCode(param.getFromNodeCode());
            entity.setRelatedNodeId(relatedNode.getRelatedNodeId());
            entity.setRelatedNodeCode(relatedNode.getRelatedNodeCode());
            entity.setCallType(param.getCallType());
            entity.setCallStatus(param.getCallStatus());
            entity.setUserTag(param.getUserTag());
            entity.setIntermediaryTag(param.getIntermediaryTag());
            entity.setRecordContent(param.getRecordContent());
            entity.setUserKey(relatedNode.getRelatedNodeCode());
            entity.setRemark("");
            entity.setCreateBy(ServletUtils.getUserName());
            entity.setUpdateBy(ServletUtils.getUserName());
            entity.setCreateTime(new Date());
            entity.setUpdateTime(new Date());
            entity.setDelFlag(0);
            list.add(entity);
        }
        return list;
    }

    private void buildRelatedNodePageList(List<NetworkRelatedUserPageVO> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        //userKey,recordCount,fraudReason
        for (NetworkRelatedUserPageVO networkRelatedUserPageVO : list) {
            //反欺诈记录数
            Integer recordCount = networkRelateNodeRecordService.queryCount(networkRelatedUserPageVO.getNodeId());
            networkRelatedUserPageVO.setRecordCount(recordCount);
            InquiryRecordDO inquiryRecordDO = inquiryRecordService.selectFinalOneByUserKey(networkRelatedUserPageVO.getNodeId());
            if (ObjectUtil.isNotNull(inquiryRecordDO)) {
                networkRelatedUserPageVO.setFraudReason(inquiryRecordDO.getConclusionDetail());
            }
        }
    }
}
