package com.weicai.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.weicai.annotation.FieldMapping;
import com.weicai.caesar.CaesarUtil;
import com.weicai.client.DcClient;
import com.weicai.client.VariableClient;
import com.weicai.entity.dto.AntiFraudDetailDTO;
import com.weicai.entity.dto.AntiFraudQueryDetailDTO;
import com.weicai.entity.JsonResult;
import com.weicai.entity.po.UserAccountDO;
import com.weicai.entity.po.UserLoanRecordInfoDO;
import com.weicai.entity.vo.AntiFraudDetailPageVO;
import com.weicai.enumeration.AntiFraudDetailEnum;
import com.weicai.mapper.di.UserLoanRecordInfoMapper;
import com.weicai.query.PageInfo;
import com.weicai.service.AntiFraudDetailService;
import com.weicai.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/1/24 17:55
 */
@Service
@Slf4j
public class AntiFraudDetailServiceImpl implements AntiFraudDetailService {

    @Value("${dp.path}")
    private String url;

    @Value("${dp.query.all.path}")
    private String dpQueryAllUrl;

    @Value("${dp.page.all.path}")
    private String dpPageAllUrl;

    @Value("${custom.service.url}")
    private String customServiceUrl;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private UserLoanRecordInfoMapper userLoanRecordInfoMapper;

    @Resource
    private UserAccountService userAccountService;

    @Resource
    private DcClient dcClient;

    @Resource
    private VariableClient variableClient;

    private static final DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public JsonResult<AntiFraudDetailPageVO> getAntiFraudDetail(AntiFraudQueryDetailDTO antiFraudQueryDetailDTO) {
        if(StringUtils.isBlank(antiFraudQueryDetailDTO.getOrder())){
            antiFraudQueryDetailDTO.setOrder("descend");
        }
        switch (antiFraudQueryDetailDTO.getRecordType()){
            case "HFQ_UPLOAD_PHONE_BOOK_RECORD":
                return hfqUploadPhoneBookRecord(antiFraudQueryDetailDTO);
            case "HFQ_UPLOAD_PHONE_CALL_RECORD":
                return hfqUploadPhoneCallRecord(antiFraudQueryDetailDTO);
            case "HFQ_UPLOAD_SMS_REPORT_RECORD":
                return hfqUploadSmsReportRecord(antiFraudQueryDetailDTO);
            case "HFQ_UPLOAD_GPS_RECORD":
                return hfqUploadGpsRecord(antiFraudQueryDetailDTO);
            case "HFQ_UPLOAD_PLIST_RECORD":
                return hfqUploadPlistRecord(antiFraudQueryDetailDTO);
            case "CUSTOM_SERVICE_RECORD":
                return customCallLogRecord(antiFraudQueryDetailDTO);
            case "HISTORY_LOAN_RECORD":
                return historyLoanRecord(antiFraudQueryDetailDTO);
            case "HFQ_UPLOAD_PHONE_INFO_RECORD":
                return hfqUploadPhoneInfoRecord(antiFraudQueryDetailDTO);
            default:
                return JsonResult.success();
        }
    }

    private JsonResult<AntiFraudDetailPageVO> hfqUploadPhoneInfoRecord(AntiFraudQueryDetailDTO antiFraudQueryDetailDTO){
        String data = queryHistoryPhoneInfo(antiFraudQueryDetailDTO.getUserKey(),
                antiFraudQueryDetailDTO.getPageNum(), antiFraudQueryDetailDTO.getPageSize(), antiFraudQueryDetailDTO.getOpTime());
        if(StringUtils.isBlank(data)){
            return JsonResult.success();
        }
        // 获取手机号
        String mobile = getMobile(antiFraudQueryDetailDTO.getUserKey());
        JSONArray records = (JSONArray) JSONPath.read(data, "$.records");
        Integer total = (Integer) JSONPath.read(data, "$.total");
        List<AntiFraudDetailDTO.UploadPhoneInfoDTO> uploadPhoneInfoDTOS = new ArrayList<>();
        for(int i = 0; i < records.size(); i++){
            JSONObject record = records.getJSONObject(i);
            AntiFraudDetailDTO.UploadPhoneInfoDTO uploadPhoneInfoDTO = buildUploadPhoneInfoDTO(record);
            uploadPhoneInfoDTO.setPhoneNumber(mobile);
            uploadPhoneInfoDTOS.add(uploadPhoneInfoDTO);
        }
        uploadPhoneInfoDTOS.sort(Comparator.comparing(AntiFraudDetailDTO.UploadPhoneInfoDTO::getReceiveTime).reversed());
        PageInfo pageInfo = PageUtil.getPageInfo(uploadPhoneInfoDTOS, total.intValue(),
                antiFraudQueryDetailDTO.getPageNum(), antiFraudQueryDetailDTO.getPageSize());
        return JsonResult.success(AntiFraudDetailPageVO.build(null, pageInfo));
    }

    private AntiFraudDetailDTO.UploadPhoneInfoDTO buildUploadPhoneInfoDTO(JSONObject record){
        JSONObject phoneInfoData = record.getJSONObject("data");
        AntiFraudDetailDTO.UploadPhoneInfoDTO uploadPhoneInfoDTO = new AntiFraudDetailDTO.UploadPhoneInfoDTO();
        uploadPhoneInfoDTO.setDeviceId((String) JSONPath.eval(phoneInfoData, "$.deviceId"));
        uploadPhoneInfoDTO.setModel((String) JSONPath.eval(phoneInfoData, "$.model"));
        uploadPhoneInfoDTO.setRoot((Boolean) JSONPath.eval(phoneInfoData, "$.root") ? "是" : "否");
        uploadPhoneInfoDTO.setCellIp((String) JSONPath.eval(phoneInfoData, "$.cellIp"));
        uploadPhoneInfoDTO.setWifiIp((String) JSONPath.eval(phoneInfoData, "$.wifiIp"));
        uploadPhoneInfoDTO.setWifiMac((String) JSONPath.eval(phoneInfoData, "$.wifiMac"));
        uploadPhoneInfoDTO.setBrand((String) JSONPath.eval(phoneInfoData, "$.brand"));
        uploadPhoneInfoDTO.setOs((String) JSONPath.eval(phoneInfoData, "$.os"));
        uploadPhoneInfoDTO.setLanguage((String) JSONPath.eval(phoneInfoData, "$.language"));
        Object receiveTimeObj = JSONPath.eval(phoneInfoData, "$.baseData.httpTimestamp");
        if(receiveTimeObj != null) {
            uploadPhoneInfoDTO.setReceiveTime(getReceiveTime(receiveTimeObj));
        }
        return uploadPhoneInfoDTO;
    }

    private String getReceiveTime(Object receiveTimeObj){
        long receiveTime = 0;
        if (receiveTimeObj instanceof Long) {
            receiveTime = (Long) receiveTimeObj;
        } else if (receiveTimeObj instanceof Integer) {
            receiveTime = ((Integer) receiveTimeObj).longValue();
        }
        if (String.valueOf(receiveTime).length() == 13) {
            receiveTime = receiveTime / 1000;
        }
        Instant instant = Instant.ofEpochSecond(receiveTime);
        return format.withZone(ZoneId.systemDefault()).format(instant);
    }

    private String getMobile(String userKey){
        LambdaQueryWrapper<UserAccountDO> wrapper = new LambdaQueryWrapper<UserAccountDO>()
                .eq(UserAccountDO::getSystemUniqueId, userKey)
                .select(UserAccountDO::getAccount);
        UserAccountDO userAccountDO = userAccountService.getOne(wrapper);
        String mobile = userAccountDO == null ? null : userAccountDO.getAccount();
        if(StringUtils.isBlank(mobile)){
            return null;
        }
        if(CaesarUtil.isEncrypted(mobile)){
            return CaesarUtil.decode(mobile);
        }
        return mobile;
    }

    private String queryHistoryPhoneInfo(String userKey, Integer pageNum,Integer pageSize, String opTime){
        String url = String.format(dpPageAllUrl, "HFQ_UPLOAD_PHONE_INFO_RECORD");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        JSONObject request = new JSONObject();
        request.put("userKey", userKey);
        request.put("pageNum", pageNum);
        request.put("pageSize", pageSize);
        opTime = StringUtils.isEmpty(opTime) ? DateUtil.getDateByTimeStamp(System.currentTimeMillis()) : opTime;
        request.put("endTime", opTime);
        HttpEntity<String> entity = new HttpEntity<>(request.toString(), headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        if (response.getStatusCodeValue() == 200 && response.getBody() != null) {
            return response.getBody();
        }
        return null;
    }

    private String queryHistoryGps(String userKey, Integer pageNum,Integer pageSize, String opTime){
        String url = String.format(dpPageAllUrl, "HFQ_UPLOAD_GPS_RECORD");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        JSONObject request = new JSONObject();
        request.put("userKey", userKey);
        request.put("pageNum", pageNum);
        request.put("pageSize", pageSize);
        opTime = StringUtils.isEmpty(opTime) ? DateUtil.getDateByTimeStamp(System.currentTimeMillis()) : opTime;
        request.put("endTime", opTime);
        HttpEntity<String> entity = new HttpEntity<>(request.toString(), headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        if (response.getStatusCodeValue() == 200 && response.getBody() != null) {
            return response.getBody();
        }
        return null;
    }

    public String queryFromCustomService(String userKey,Integer pageNum,Integer pageSize,String opTime){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        JSONObject request = new JSONObject();
        request.put("userKey", userKey);
        request.put("pageNum", pageNum);
        request.put("pageSize", pageSize);
        opTime = StringUtils.isEmpty(opTime) ? DateUtil.getDateByTimeStamp(System.currentTimeMillis()) : opTime;
        request.put("endTime", opTime);
        request.put("startTime",getStartTime(opTime));
        log.info("queryFromCustomService param = {} ",JSON.toJSONString(request));
        HttpEntity<String> entity = new HttpEntity<>(request.toString(), headers);
        ResponseEntity<String> response = restTemplate.exchange(customServiceUrl, HttpMethod.POST, entity, String.class);
        if (response.getStatusCodeValue() == 200 && response.getBody() != null) {
            return response.getBody();
        }
        return null;
    }

    private String getStartTime(String endTime){
        LocalDateTime end = LocalDateTime.parse(endTime, format);
        LocalDateTime start = end.minusYears(1);
        return start.format(format);
    }

    private String queryRecentNotNullPlistFromBabel(String userKey, String opTime){
        String url = String.format(dpQueryAllUrl, "HFQ_UPLOAD_PLIST_RECORD", userKey);
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        if (response.getStatusCodeValue() == 200 && response.getBody() != null) {
            JSONObject recentNotNullPlist = new JSONObject();
            JSONObject plistAll = JSONObject.parseObject(response.getBody());
            JSONArray records = plistAll.getJSONArray("records");
            if(records.isEmpty()){
                return null;
            }
            JSONObject notNullAppList = (JSONObject) records.stream().filter(record -> {
                JSONObject recordJson = (JSONObject) record;
                JSONArray appList = (JSONArray) JSONPath.eval(recordJson, "$.data.appList");
                boolean withinTime = true;
                if(StringUtils.isNotBlank(opTime)) {
                    // opTime转换为时间戳
                    long opTimeStamp = DateUtil.getTimeStamp(opTime);
                    Object httpTimestampObj = JSONPath.eval(recordJson, "$.data.baseData.httpTimestamp");
                    long httpTimestamp = 0;
                    if(httpTimestampObj instanceof Long){
                        httpTimestamp = (Long) httpTimestampObj;
                    }else if(httpTimestampObj instanceof Integer){
                        httpTimestamp = ((Integer) httpTimestampObj).longValue();
                    }
                    if(String.valueOf(httpTimestamp).length() == 10){
                        httpTimestamp = httpTimestamp * 1000;
                    }
                    withinTime = opTimeStamp >= httpTimestamp;
                }
                return appList != null && withinTime;
            }).findFirst().orElse(records.get(0));
            JSONArray newArray = new JSONArray();
            newArray.add(notNullAppList);
            recentNotNullPlist.put("records", newArray);
            return recentNotNullPlist.toJSONString();
        }
        return null;
    }

    /**
     * 从babel接口获取数据
     */
    public String queryFromBabelRead(String userKey,String endTime,Integer days,String type){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        JSONObject request = new JSONObject();
        request.put("userKey", userKey);
        endTime = StringUtils.isEmpty(endTime) ? DateUtil.getDateByTimeStamp(System.currentTimeMillis()): endTime;
        request.put("endTime", endTime);
        request.put("days", days);
        request.put("systemId", "HAO_HUAN");
        log.info("queryFromBabelRead param = {} ",JSON.toJSONString(request));
        HttpEntity<String> entity = new HttpEntity<>(request.toString(), headers);
        String urlPath = String.format(url,type);
        ResponseEntity<String> response = restTemplate.exchange(urlPath, HttpMethod.POST, entity, String.class);
        if (response.getStatusCodeValue() == 200 && response.getBody() != null) {
            return response.getBody();
        }
        return null;
    }

    private JsonResult<AntiFraudDetailPageVO> historyLoanRecord(AntiFraudQueryDetailDTO antiFraudQueryDetailDTO){
        String endTime = StringUtils.isEmpty(antiFraudQueryDetailDTO.getOpTime())
                ? DateUtil.getDateByTimeStamp(System.currentTimeMillis()): antiFraudQueryDetailDTO.getOpTime();
        String startTime = getStartTime(endTime);
        LambdaQueryWrapper<UserLoanRecordInfoDO> wrapper = new LambdaQueryWrapper<UserLoanRecordInfoDO>()
                .eq(UserLoanRecordInfoDO::getUserKey, antiFraudQueryDetailDTO.getUserKey())
                .isNotNull(UserLoanRecordInfoDO::getDzCreateTime)
                .ne(UserLoanRecordInfoDO::getDzCreateTime, "")
                .between(UserLoanRecordInfoDO::getDzCreateTime, startTime, endTime);
        List<UserLoanRecordInfoDO> list = userLoanRecordInfoMapper.selectList(wrapper);
        if(list.stream().noneMatch(t -> t.getLoanKey().equals(antiFraudQueryDetailDTO.getLoanKey()))){
            // 当天的记录查询dc获取
            if(StringUtils.isNoneBlank(antiFraudQueryDetailDTO.getLoanKey(), antiFraudQueryDetailDTO.getUserKey())) {
                UserLoanRecordInfoDO currentRecord = buildCurrentRecord(antiFraudQueryDetailDTO.getUserKey(), antiFraudQueryDetailDTO.getLoanKey());
                list.add(currentRecord);
            }
        }
        if("ascend".equals(antiFraudQueryDetailDTO.getOrder())){
            list.sort(Comparator.comparing(UserLoanRecordInfoDO::getDzCreateTime));
        }else {
            list.sort(Comparator.comparing(UserLoanRecordInfoDO::getDzCreateTime).reversed());
        }
        int startPosition = (antiFraudQueryDetailDTO.getPageNum() - 1) * antiFraudQueryDetailDTO.getPageSize();
        Stream<UserLoanRecordInfoDO> stream = list.stream().skip(startPosition).limit(antiFraudQueryDetailDTO.getPageSize());
        PageInfo<UserLoanRecordInfoDO> pageInfo = new PageInfo<>();
        pageInfo.setList(stream.collect(Collectors.toList()));
        pageInfo.setPageNum(antiFraudQueryDetailDTO.getPageNum());
        pageInfo.setPageSize(antiFraudQueryDetailDTO.getPageSize());
        pageInfo.setTotal(list.size());
        AntiFraudDetailPageVO antiFraudDetailPageVO = AntiFraudDetailPageVO.build(null, pageInfo);
        return JsonResult.success(antiFraudDetailPageVO);
    }

    private UserLoanRecordInfoDO buildCurrentRecord(String userKey, String loanKey) {
        UserLoanRecordInfoDO userLoanRecordInfoDO = new UserLoanRecordInfoDO();
        userLoanRecordInfoDO.setUserKey(userKey);
        userLoanRecordInfoDO.setLoanKey(loanKey);
        try {
            JSONArray variableList = variableClient.queryVariable(userKey,loanKey,"quotaServiceVarGroupForFraudCase",1);
            Map<String,String> variableMap = variableList.stream().collect(Collectors.toMap(item -> ((JSONObject) item).getString("code"),
                    item -> ((JSONObject) item).getString("value")));
            JSONObject originInput = dcClient.queryOriginInput(loanKey);
            String loanSubmitTime = (String) JSONPath.eval(originInput, "$.loanSubmitTime");
            if(StringUtils.isNotBlank(loanSubmitTime)){
                // 转为时间
                userLoanRecordInfoDO.setDzCreateTime(DateUtil.getDateByTimeStamp(Long.parseLong(loanSubmitTime)));
            }else {
                userLoanRecordInfoDO.setDzCreateTime(DateUtil.getDateByTimeStamp(System.currentTimeMillis()));
            }
            userLoanRecordInfoDO.setApplyAmount((String) JSONPath.eval(originInput, "$.amount"));
            userLoanRecordInfoDO.setApplyPeriod((String) JSONPath.eval(originInput, "$.periods"));
            userLoanRecordInfoDO.setApplyRate((String) JSONPath.eval(originInput, "$.rate"));
            userLoanRecordInfoDO.setLoanLine(variableMap.get("or_total_amount"));
            userLoanRecordInfoDO.setLoanActualLine(variableMap.get("or_avail_amount"));
        }catch (Exception e){
            log.error("调用dc获取数据失败,userKey:{},loanKey:{}",userKey,loanKey,e);
        }
        return userLoanRecordInfoDO;
    }

    private JsonResult<AntiFraudDetailPageVO> customCallLogRecord(AntiFraudQueryDetailDTO antiFraudQueryDetailDTO) {
        String body = queryFromCustomService(antiFraudQueryDetailDTO.getUserKey(), antiFraudQueryDetailDTO.getPageNum(),
                antiFraudQueryDetailDTO.getPageSize(), antiFraudQueryDetailDTO.getOpTime());
        if(StringUtils.isBlank(body)){
            return JsonResult.success();
        }
        Integer total = (Integer) JSONPath.read(body, "$.data.total");
        JSONArray customCallLogRecord = (JSONArray) JSONPath.read(body, "$.data.data");
        List<AntiFraudDetailDTO.CustomCallLogRecordDTO> customCallLogRecordDTOS = null;
        if (customCallLogRecord != null) {
            customCallLogRecordDTOS = customCallLogRecord.stream().map(item -> {
                JSONObject itemObject = (JSONObject) item;
                AntiFraudDetailDTO.CustomCallLogRecordDTO customCallLogRecordDTO = new AntiFraudDetailDTO.CustomCallLogRecordDTO();
                customCallLogRecordDTO.setCrtDeptName(itemObject.getString("crtDeptName"));
                customCallLogRecordDTO.setCrtDt(itemObject.getString("crtDt"));
                customCallLogRecordDTO.setCrtName(itemObject.getString("crtName"));
                customCallLogRecordDTO.setFromTypeName(itemObject.getString("fromTypeName"));
                customCallLogRecordDTO.setFromWayName(itemObject.getString("fromWayName"));
                customCallLogRecordDTO.setItemName(itemObject.getString("itemName"));
                customCallLogRecordDTO.setRemark(itemObject.getString("remark"));
                customCallLogRecordDTO.setServiceTypeName(itemObject.getString("serviceTypeName"));
                customCallLogRecordDTO.setThreeTypeName(itemObject.getString("threeTypeName"));
                return customCallLogRecordDTO;
            }).collect(Collectors.toList());
        }
        PageInfo<AntiFraudDetailDTO.CustomCallLogRecordDTO> pageInfo = new PageInfo<>();
        pageInfo.setList(customCallLogRecordDTOS);
        pageInfo.setPageNum(antiFraudQueryDetailDTO.getPageNum());
        pageInfo.setPageSize(antiFraudQueryDetailDTO.getPageSize());
        pageInfo.setTotal(total == null ? 0 : total);
        AntiFraudDetailPageVO antiFraudDetailPageVO = AntiFraudDetailPageVO.build(null, pageInfo);
        return JsonResult.success(antiFraudDetailPageVO);
    }

    /**
     * plist
     * <a href="http://babel-read.test.rrdbg.com/v1/record/recentTime/user/HFQ_UPLOAD_PLIST_RECORD">...</a>
     */
    private JsonResult<AntiFraudDetailPageVO> hfqUploadPlistRecord(AntiFraudQueryDetailDTO antiFraudQueryDetailDTO) {
        String body = queryRecentNotNullPlistFromBabel(antiFraudQueryDetailDTO.getUserKey(), antiFraudQueryDetailDTO.getOpTime());
        if(StringUtils.isBlank(body)){
            return JsonResult.success();
        }
        AntiFraudDetailDTO antiFraudDetailDTO  = new AntiFraudDetailDTO();
        baseData(body, antiFraudDetailDTO);

        JSONArray appList = (JSONArray) JSONPath.read(body, "$.records.data[0].appList");
        List<AntiFraudDetailDTO.UploadAppDetailDTO> uploadAppDetailDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(appList)){
            Field[] uploadAppDetails = AntiFraudDetailDTO.UploadAppDetailDTO.class.getDeclaredFields();
            for (int i = 0 ; i < appList.size() ; i++){
                JSONObject appInfo = appList.getJSONObject(i);
                AntiFraudDetailDTO.UploadAppDetailDTO uploadAppDetailDTO = new AntiFraudDetailDTO.UploadAppDetailDTO();

                for (Field declaredField : uploadAppDetails) {
                    FieldMapping annotation = declaredField.getAnnotation(FieldMapping.class);
                    // Date日期转为Str
                    if (annotation != null && annotation.date() && appInfo.get(declaredField.getName()) != null) {
                        Date date = appInfo.getDate(declaredField.getName());
                        if(date != null) {
                            ReflectionUtils.setFieldValue(uploadAppDetailDTO, declaredField.getName(), DateUtil.convertDateToStr(date));
                        }
                    } else {
                        ReflectionUtils.setFieldValue(uploadAppDetailDTO, declaredField.getName(), appInfo.get(declaredField.getName()));
                    }
                }
                uploadAppDetailDTOS.add(uploadAppDetailDTO);
            }
        }
        // 排序
        try {
            uploadAppDetailDTOS.sort((o1, o2) -> {
                String sortField = antiFraudQueryDetailDTO.getField();
                if (sortField.equals("lastUpdateTime")) {
                    if (antiFraudQueryDetailDTO.getOrder().equals("descend")) {
                        return o2.getLastUpdateTime().compareTo(o1.getLastUpdateTime());
                    }
                    return o1.getLastUpdateTime().compareTo(o2.getLastUpdateTime());
                } else {
                    if (antiFraudQueryDetailDTO.getOrder().equals("descend")) {
                        return o2.getLastOpenTime().compareTo(o1.getLastOpenTime());
                    }
                    return o1.getLastOpenTime().compareTo(o2.getLastOpenTime());
                }
            });
        }catch (Exception e){
            log.error("排序异常",e);
        }
        PageInfo pageInfo = PageUtil.getPageInfo(uploadAppDetailDTOS, antiFraudQueryDetailDTO.getPageNum(), antiFraudQueryDetailDTO.getPageSize());
        AntiFraudDetailPageVO antiFraudDetailPageVO = AntiFraudDetailPageVO.build(antiFraudDetailDTO, pageInfo);
        return JsonResult.success(antiFraudDetailPageVO);
    }

    /**
     * GPS明细
     * <a href="http://babel-read.test.rrdbg.com/v1/record/recentTime/user/HFQ_UPLOAD_GPS_RECORD">...</a>
     */
    private JsonResult<AntiFraudDetailPageVO> hfqUploadGpsRecord(AntiFraudQueryDetailDTO antiFraudQueryDetailDTO) {
        String body = queryHistoryGps(antiFraudQueryDetailDTO.getUserKey(),
                antiFraudQueryDetailDTO.getPageNum(), antiFraudQueryDetailDTO.getPageSize(),
                antiFraudQueryDetailDTO.getOpTime());

        if (StringUtils.isEmpty(body)){
            return JsonResult.success();
        }
        Integer total = (Integer) JSONPath.read(body, "$.total");
        AntiFraudDetailDTO antiFraudDetailDTO  = new AntiFraudDetailDTO();
        baseData(body, antiFraudDetailDTO);
        List<AntiFraudDetailDTO.UploadGpsDetailDTO> uploadSmsDetailDTOS = new ArrayList<>();

        JSONArray records = (JSONArray) JSONPath.read(body, "$.records");
        for(int i = 0; i < records.size(); i++){
            JSONObject record = records.getJSONObject(i);
            JSONObject data = record.getJSONObject("data");
            if(Objects.isNull(data)){
                continue;
            }
            AntiFraudDetailDTO.UploadGpsDetailDTO uploadGpsDetailDTO = new AntiFraudDetailDTO.UploadGpsDetailDTO();
            uploadGpsDetailDTO.setLatitude(data.getDouble("latitude"));
            uploadGpsDetailDTO.setLongitude(data.getDouble("longitude"));
            uploadGpsDetailDTO.setProvince(data.getString("province"));
            uploadGpsDetailDTO.setCity(data.getString("city"));
            uploadGpsDetailDTO.setAddress(data.getString("address"));
            uploadGpsDetailDTO.setAltitude(data.getDouble("altitude"));
            Object receiveTimeObj = JSONPath.eval(data, "$.baseData.httpTimestamp");
            if(receiveTimeObj != null) {
                uploadGpsDetailDTO.setHttpTimestamp(getReceiveTime(receiveTimeObj));
            }
            uploadGpsDetailDTO.setUploadInfoOpportunity((String) JSONPath.eval(data, "$.baseData.uploadInfoOpportunity"));
            uploadGpsDetailDTO.setVersion((String) JSONPath.eval(data, "$.baseData.version"));
            uploadGpsDetailDTO.setOs((String) JSONPath.eval(data, "$.baseData.os"));
            Integer isAuthorized = (Integer) JSONPath.eval(data, "$.baseData.isAuthorized");
            if(isAuthorized != null) {
                Object value = AntiFraudDetailEnum.IsAuthorizedEnum.getValue(isAuthorized);
                uploadGpsDetailDTO.setIsAuthorized(value == null ? "" : value.toString());
            }
            uploadSmsDetailDTOS.add(uploadGpsDetailDTO);
        }
        // 分页
        PageInfo pageInfo = PageUtil.getPageInfo(uploadSmsDetailDTOS, total,
                antiFraudQueryDetailDTO.getPageNum(), antiFraudQueryDetailDTO.getPageSize());
        AntiFraudDetailPageVO antiFraudDetailPageVO = AntiFraudDetailPageVO.build(antiFraudDetailDTO, pageInfo);
        return JsonResult.success(antiFraudDetailPageVO);
    }

    /**
     * 短信明细
     * <a href="http://babel-read.test.rrdbg.com/v1/record/recentTime/user/HFQ_UPLOAD_SMS_REPORT_RECORD">...</a>
     */
    private JsonResult<AntiFraudDetailPageVO> hfqUploadSmsReportRecord(AntiFraudQueryDetailDTO antiFraudQueryDetailDTO) {
        String body = queryFromBabelRead(antiFraudQueryDetailDTO.getUserKey(),
                antiFraudQueryDetailDTO.getOpTime(), antiFraudQueryDetailDTO.getDay(), antiFraudQueryDetailDTO.getRecordType());
        if (StringUtils.isEmpty(body)){
            return JsonResult.success();
        }
        AntiFraudDetailDTO antiFraudDetailDTO  = new AntiFraudDetailDTO();
        baseData(body, antiFraudDetailDTO);

        JSONArray smsInfoList = (JSONArray) JSONPath.read(body, "$.records.data[0].smsInfoList");
        List<AntiFraudDetailDTO.UploadSmsDetailDTO> uploadSmsDetailDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(smsInfoList)){
            Field[] uploadSmsDetails = AntiFraudDetailDTO.UploadSmsDetailDTO.class.getDeclaredFields();
            for (int i = 0 ; i < smsInfoList.size() ; i++){
                JSONObject smsInfo = smsInfoList.getJSONObject(i);
                AntiFraudDetailDTO.UploadSmsDetailDTO uploadSmsDetailDTO = new AntiFraudDetailDTO.UploadSmsDetailDTO();

                for (Field declaredField : uploadSmsDetails) {
                    FieldMapping annotation = declaredField.getAnnotation(FieldMapping.class);

                    if (annotation != null && annotation.date() && smsInfo.get(declaredField.getName()) != null) {
                        ReflectionUtils.setFieldValue(uploadSmsDetailDTO, declaredField.getName(), DateUtil.getDateByTimeStamp(smsInfo.getLong(declaredField.getName())));
                    } else if (annotation != null && annotation.enumClass() != Void.class && smsInfo.get(declaredField.getName()) != null) {
                        ReflectionUtils.setFieldValue(uploadSmsDetailDTO, declaredField.getName(), EnumConverterUtil.convertToEnum(smsInfo.get(declaredField.getName()), annotation.enumClass()));
                    } else {
                        ReflectionUtils.setFieldValue(uploadSmsDetailDTO, declaredField.getName(), smsInfo.get(declaredField.getName()));
                    }
                }
                uploadSmsDetailDTOS.add(uploadSmsDetailDTO);
            }
        }
        // 排序
        try {
            uploadSmsDetailDTOS.sort((o1, o2) -> {
                if (antiFraudQueryDetailDTO.getOrder().equals("descend")) {
                    return o2.getDate().compareTo(o1.getDate());
                }
                return o1.getDate().compareTo(o2.getDate());
            });
        }catch (Exception e){
            log.error("排序异常",e);
        }
        if(antiFraudQueryDetailDTO.getMask() == 1){
            // 脱敏
            for(AntiFraudDetailDTO.UploadSmsDetailDTO uploadSmsDetailDTO : uploadSmsDetailDTOS){
                uploadSmsDetailDTO.setAddress(SensitiveUtils.maskPhoneNumber(uploadSmsDetailDTO.getAddress()));
                uploadSmsDetailDTO.setPerson(SensitiveUtils.maskName(uploadSmsDetailDTO.getPerson()));
            }
        }
        PageInfo pageInfo = PageUtil.getPageInfo(uploadSmsDetailDTOS, antiFraudQueryDetailDTO.getPageNum(), antiFraudQueryDetailDTO.getPageSize());
        AntiFraudDetailPageVO antiFraudDetailPageVO = AntiFraudDetailPageVO.build(antiFraudDetailDTO, pageInfo);
        return JsonResult.success(antiFraudDetailPageVO);
    }

    /**
     * 通讯录明细
     * <a href="http://babel-read.test.rrdbg.com/v1/record/recentTime/user/HFQ_UPLOAD_PHONE_BOOK_RECORD">...</a>
     */
    public JsonResult<AntiFraudDetailPageVO> hfqUploadPhoneBookRecord(AntiFraudQueryDetailDTO antiFraudQueryDetailDTO){
        String body = queryFromBabelRead(antiFraudQueryDetailDTO.getUserKey(),
                antiFraudQueryDetailDTO.getOpTime(), antiFraudQueryDetailDTO.getDay(), antiFraudQueryDetailDTO.getRecordType());
        if (StringUtils.isEmpty(body)){
            return JsonResult.success();
        }
        AntiFraudDetailDTO antiFraudDetailDTO  = new AntiFraudDetailDTO();

        baseData(body, antiFraudDetailDTO);

        JSONArray phoneBookList = (JSONArray) JSONPath.read(body, "$.records.data[0].phoneBookList");
        List<AntiFraudDetailDTO.UploadPhoneBookDetailDTO> uploadPhoneBookDetailDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(phoneBookList)) {
            Field[] uploadPhoneBookDetails = AntiFraudDetailDTO.UploadPhoneBookDetailDTO.class.getDeclaredFields();
            for (int i = 0; i < phoneBookList.size(); i++) {
                JSONObject phoneBook = phoneBookList.getJSONObject(i);
                AntiFraudDetailDTO.UploadPhoneBookDetailDTO uploadPhoneBookDetailDTO = new AntiFraudDetailDTO.UploadPhoneBookDetailDTO();
                for (Field declaredField : uploadPhoneBookDetails) {
                    FieldMapping annotation = declaredField.getAnnotation(FieldMapping.class);
                    if (annotation != null && annotation.date() && phoneBook.get(declaredField.getName()) != null) {
                        Object val = phoneBook.get(declaredField.getName());
                        if(val instanceof Long) {
                            ReflectionUtils.setFieldValue(uploadPhoneBookDetailDTO, declaredField.getName(), DateUtil.getDateByTimeStamp(phoneBook.getLong(declaredField.getName())));
                        }
                        if (val instanceof String){
                            String date = DateUtil.transformSpecialDate((String) val);
                            ReflectionUtils.setFieldValue(uploadPhoneBookDetailDTO, declaredField.getName(), date);
                        }
                    } else {
                        ReflectionUtils.setFieldValue(uploadPhoneBookDetailDTO, declaredField.getName(), phoneBook.get(declaredField.getName()));
                    }
                }
                uploadPhoneBookDetailDTOS.add(uploadPhoneBookDetailDTO);
            }
        }
        // 排序
        try {
            uploadPhoneBookDetailDTOS.sort((o1, o2) -> {
                String sortField = antiFraudQueryDetailDTO.getField();
                if (sortField.equals("modifyTime")) {
                    if (antiFraudQueryDetailDTO.getOrder().equals("descend")) {
                        return o2.getModifyTime().compareTo(o1.getModifyTime());
                    }
                    return o1.getModifyTime().compareTo(o2.getModifyTime());
                } else {
                    if (antiFraudQueryDetailDTO.getOrder().equals("descend")) {
                        return o2.getCreateTime().compareTo(o1.getCreateTime());
                    }
                    return o1.getCreateTime().compareTo(o2.getCreateTime());
                }
            });
        }catch (Exception e){
            log.error("排序异常",e);
        }
        // 脱敏
        if(antiFraudQueryDetailDTO.getMask() == 1){
            for(AntiFraudDetailDTO.UploadPhoneBookDetailDTO uploadPhoneBookDetailDTO : uploadPhoneBookDetailDTOS){
                uploadPhoneBookDetailDTO.setMobile(SensitiveUtils.maskPhoneNumber(uploadPhoneBookDetailDTO.getMobile()));
                uploadPhoneBookDetailDTO.setName(SensitiveUtils.maskName(uploadPhoneBookDetailDTO.getName()));
            }
        }
        // 分页
        PageInfo pageInfo = PageUtil.getPageInfo(uploadPhoneBookDetailDTOS, antiFraudQueryDetailDTO.getPageNum(), antiFraudQueryDetailDTO.getPageSize());
        AntiFraudDetailPageVO antiFraudDetailPageVO = AntiFraudDetailPageVO.build(antiFraudDetailDTO, pageInfo);
        return JsonResult.success(antiFraudDetailPageVO);
    }

    /**
     * <a href="http://babel-read.test.rrdbg.com/v1/record/recentTime/user/HFQ_UPLOAD_PHONE_CALL_RECORD">...</a>
     */
    private JsonResult<AntiFraudDetailPageVO> hfqUploadPhoneCallRecord(AntiFraudQueryDetailDTO antiFraudQueryDetailDTO) {
        String body = queryFromBabelRead(antiFraudQueryDetailDTO.getUserKey(),
                antiFraudQueryDetailDTO.getOpTime(), antiFraudQueryDetailDTO.getDay(), antiFraudQueryDetailDTO.getRecordType());
        if (StringUtils.isEmpty(body)){
            return JsonResult.success();
        }
        AntiFraudDetailDTO antiFraudDetailDTO  = new AntiFraudDetailDTO();

        baseData(body, antiFraudDetailDTO);

        JSONArray phoneCallList = (JSONArray) JSONPath.read(body, "$.records.data[0].phoneCallList");

        List<AntiFraudDetailDTO.UploadPhoneCallDetailDTO> uploadPhoneCallDetailDTOS = new ArrayList<>();

        if (!CollectionUtils.isEmpty(phoneCallList)){
            Field[] uploadPhoneCallDetails = AntiFraudDetailDTO.UploadPhoneCallDetailDTO.class.getDeclaredFields();
            for (int i = 0; i < phoneCallList.size(); i++) {
                JSONObject phoneCall = phoneCallList.getJSONObject(i);
                AntiFraudDetailDTO.UploadPhoneCallDetailDTO uploadPhoneCallDetailDTO = new AntiFraudDetailDTO.UploadPhoneCallDetailDTO();
                for (Field declaredField : uploadPhoneCallDetails) {
                    ReflectionUtils.setFieldValue(uploadPhoneCallDetailDTO, declaredField.getName(),
                            phoneCall.get(declaredField.getName()));
                    FieldMapping annotation = declaredField.getAnnotation(FieldMapping.class);
                    if (annotation != null && annotation.date() && phoneCall.get(declaredField.getName()) != null) {
                        Object val = phoneCall.get(declaredField.getName());
                        if(val instanceof Long) {
                            ReflectionUtils.setFieldValue(uploadPhoneCallDetailDTO, declaredField.getName(),
                                    DateUtil.getDateByTimeStamp(phoneCall.getLong(declaredField.getName())));
                        }
                        if (val instanceof String){
                            String date = DateUtil.transformSpecialDate((String) val);
                            ReflectionUtils.setFieldValue(uploadPhoneCallDetailDTO, declaredField.getName(), date);
                        }
                    }
                }
                uploadPhoneCallDetailDTOS.add(uploadPhoneCallDetailDTO);
            }
        }
        // 排序
        try {
            uploadPhoneCallDetailDTOS.sort((o1, o2) -> {
                if (antiFraudQueryDetailDTO.getOrder().equals("descend")) {
                    return o2.getDate().compareTo(o1.getDate());
                }
                return o1.getDate().compareTo(o2.getDate());
            });
        }catch (Exception e){
            log.error("排序异常",e);
        }
        // 脱敏
        if(antiFraudQueryDetailDTO.getMask() == 1){
            for(AntiFraudDetailDTO.UploadPhoneCallDetailDTO uploadPhoneCallDetailDTO : uploadPhoneCallDetailDTOS){
                uploadPhoneCallDetailDTO.setNumber(SensitiveUtils.maskPhoneNumber(uploadPhoneCallDetailDTO.getNumber()));
                uploadPhoneCallDetailDTO.setName(SensitiveUtils.maskName(uploadPhoneCallDetailDTO.getName()));
            }
        }
        /** 分页 **/
        PageInfo pageInfo = PageUtil.getPageInfo(uploadPhoneCallDetailDTOS, antiFraudQueryDetailDTO.getPageNum(), antiFraudQueryDetailDTO.getPageSize());
        AntiFraudDetailPageVO antiFraudDetailPageVO = AntiFraudDetailPageVO.build(antiFraudDetailDTO, pageInfo);
        return JsonResult.success(antiFraudDetailPageVO);
    }


    /**
     * base字段一致: 通用处理,不需要映射字段
     */
    private static void baseData(String body, AntiFraudDetailDTO antiFraudDetailDTO) {
        JSONObject baseData = (JSONObject) JSONPath.read(body, "$.records.data[0].baseData");
        if(baseData == null){
            return;
        }
        Field[] declaredFields = AntiFraudDetailDTO.class.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            FieldMapping annotation = declaredField.getAnnotation(FieldMapping.class);
            String name = declaredField.getName();
            if (annotation != null) {
                boolean date = annotation.date();
                if (date && baseData.getLong(name) != null) {
                    ReflectionUtils.setFieldValue(antiFraudDetailDTO, name, DateUtil.getDateByTimeStamp(baseData.getLong(name)));
                }
                Class aClass = annotation.enumClass();
                if (aClass != Void.class && baseData.get(name) != null) {
                    ReflectionUtils.setFieldValue(antiFraudDetailDTO, name, EnumConverterUtil.convertToEnum(baseData.get(name), aClass));
                }
            } else {
                ReflectionUtils.setFieldValue(antiFraudDetailDTO, name, baseData.get(name));
            }
        }
    }

}
