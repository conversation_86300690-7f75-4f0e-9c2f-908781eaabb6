package com.weicai.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weicai.entity.dto.NetworkRelatedNodeRecordQueryDTO;
import com.weicai.entity.po.RaNetworkRelatedNodeRecordDO;
import com.weicai.mapper.di.RaNetworkRelatedNodeRecordMapper;
import com.weicai.query.PageInfo;
import com.weicai.service.NetworkRelateNodeRecordService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: TODO
 * Date: 2025/5/26
 */
@Service
public class NetworkRelateNodeRecordServiceImpl extends ServiceImpl<RaNetworkRelatedNodeRecordMapper, RaNetworkRelatedNodeRecordDO> implements NetworkRelateNodeRecordService {
    @Override
    public PageInfo<RaNetworkRelatedNodeRecordDO> queryRelatedNodeRecordPageList(NetworkRelatedNodeRecordQueryDTO param) {
        LambdaQueryWrapper<RaNetworkRelatedNodeRecordDO> wrapper = new LambdaQueryWrapper<RaNetworkRelatedNodeRecordDO>()
                .eq(RaNetworkRelatedNodeRecordDO::getRelatedNodeId, param.getRelatedNodeId())
                .eq(RaNetworkRelatedNodeRecordDO::getDelFlag, 0)
                .orderByDesc(RaNetworkRelatedNodeRecordDO::getUpdateTime);
        IPage<RaNetworkRelatedNodeRecordDO> page = new Page<>(param.getPageNum(), param.getPageSize());
        IPage<RaNetworkRelatedNodeRecordDO> pageList = baseMapper.selectPage(page, wrapper);
        PageInfo<RaNetworkRelatedNodeRecordDO> result = new PageInfo<>();
        result.setPageNum((int) pageList.getCurrent());
        result.setPageSize((int) pageList.getSize());
        result.setTotal(pageList.getTotal());
        result.setPages((int) pageList.getPages());
        result.setList(pageList.getRecords());
        return result;
    }

    @Override
    public Integer queryCount(String nodeId) {
        LambdaQueryWrapper<RaNetworkRelatedNodeRecordDO> wrapper = new LambdaQueryWrapper<RaNetworkRelatedNodeRecordDO>()
                .eq(RaNetworkRelatedNodeRecordDO::getRelatedNodeId, nodeId)
                .eq(RaNetworkRelatedNodeRecordDO::getDelFlag, 0);
       return baseMapper.selectCount(wrapper);
    }
}
