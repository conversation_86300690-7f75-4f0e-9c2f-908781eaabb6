package com.weicai.service.impl;

import com.weicai.constants.RedisKeyConstants;
import com.weicai.redis.RedisService;
import com.weicai.service.ManualAuditStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 人工审核状态查询服务实现
 * 
 * <AUTHOR>
 * @since 2024/12/26
 */
@Service
@Slf4j
public class ManualAuditStatusServiceImpl implements ManualAuditStatusService {
    
    @Resource
    private RedisService redisService;
    
    @Override
    public boolean isInManualAudit(String userKey) {
        if (!StringUtils.hasText(userKey)) {
            log.warn("userKey为空，返回false");
            return false;
        }
        
        try {
            String redisKey = RedisKeyConstants.MANUAL_AUDIT_STATUS + userKey;
            String value = redisService.get(redisKey);
            
            // 如果Redis中存在该key且值为"1"，则表示正在审核中
            boolean isInAudit = "1".equals(value);
            
            log.debug("查询用户 {} 审核状态: {}", userKey, isInAudit ? "审核中" : "未审核");
            
            return isInAudit;
            
        } catch (Exception e) {
            log.error("查询用户 {} 审核状态失败", userKey, e);
            // 发生异常时返回false，避免影响业务流程
            return false;
        }
    }
    
    @Override
    public void enterAuditStatus(String userKey, long expireSeconds) {
        if (!StringUtils.hasText(userKey)) {
            log.warn("userKey为空，无法设置审核状态");
            return;
        }
        
        try {
            String redisKey = RedisKeyConstants.MANUAL_AUDIT_STATUS + userKey;
            redisService.setEx(redisKey, "1", expireSeconds, TimeUnit.SECONDS);
            log.info("手动设置用户 {} 进入审核状态，过期时间: {} 秒", userKey, expireSeconds);
            
        } catch (Exception e) {
            log.error("设置用户 {} 进入审核状态失败", userKey, e);
        }
    }
    
    @Override
    public void exitAuditStatus(String userKey) {
        if (!StringUtils.hasText(userKey)) {
            log.warn("userKey为空，无法删除审核状态");
            return;
        }
        
        try {
            String redisKey = RedisKeyConstants.MANUAL_AUDIT_STATUS + userKey;
            redisService.delete(redisKey);
            log.info("手动设置用户 {} 退出审核状态", userKey);
            
        } catch (Exception e) {
            log.error("设置用户 {} 退出审核状态失败", userKey, e);
        }
    }

    @Override
    public void batchEnterAuditStatus(java.util.List<String> userKeys, long expireSeconds) {
        if (userKeys == null || userKeys.isEmpty()) {
            log.warn("userKeys为空，无法批量设置审核状态");
            return;
        }

        try {
            for (String userKey : userKeys) {
                if (StringUtils.hasText(userKey)) {
                    String redisKey = RedisKeyConstants.MANUAL_AUDIT_STATUS + userKey;
                    redisService.setEx(redisKey, "1", expireSeconds, TimeUnit.SECONDS);
                }
            }
            log.info("批量设置 {} 个用户进入审核状态，过期时间: {} 秒", userKeys.size(), expireSeconds);

        } catch (Exception e) {
            log.error("批量设置用户进入审核状态失败，userKeys: {}", userKeys, e);
        }
    }

    @Override
    public void batchExitAuditStatus(java.util.List<String> userKeys) {
        if (userKeys == null || userKeys.isEmpty()) {
            log.warn("userKeys为空，无法批量删除审核状态");
            return;
        }

        try {
            java.util.List<String> redisKeys = userKeys.stream()
                    .filter(StringUtils::hasText)
                    .map(userKey -> RedisKeyConstants.MANUAL_AUDIT_STATUS + userKey)
                    .collect(java.util.stream.Collectors.toList());

            if (!redisKeys.isEmpty()) {
                redisService.delete(redisKeys);
            }
            log.info("批量设置 {} 个用户退出审核状态", userKeys.size());

        } catch (Exception e) {
            log.error("批量设置用户退出审核状态失败，userKeys: {}", userKeys, e);
        }
    }
}
