package com.weicai.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weicai.entity.po.InquiryRecordDO;
import com.weicai.entity.vo.ConclusionEnumVO;
import com.weicai.entity.vo.InquiryRecordVO;
import com.weicai.enumeration.ConclusionTypeEnum;
import com.weicai.mapper.di.InquiryRecordMapper;
import com.weicai.nacos.NacosClientAdapter;
import com.weicai.service.InquiryRecordService;
import com.weicai.utils.LocalDateTimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/20 16:26
 */
@Service
public class InquiryRecordServiceImpl extends ServiceImpl<InquiryRecordMapper, InquiryRecordDO> implements InquiryRecordService {
    @Override
    public List<InquiryRecordDO> selectByLoanKeys(List<String> loanKeys) {
        if(CollectionUtils.isEmpty(loanKeys)){
            return new ArrayList<>();
        }
        List<InquiryRecordDO> latestRecord = new ArrayList<>();
        LambdaQueryWrapper<InquiryRecordDO> wrapper = new LambdaQueryWrapper<InquiryRecordDO>()
                .in(InquiryRecordDO::getLoanKey, loanKeys)
                .orderByDesc(InquiryRecordDO::getCreateTime);
        List<InquiryRecordDO> inquiryRecords = baseMapper.selectList(wrapper);
        // 按loanKey分组
        Map<String, List<InquiryRecordDO>> groupMap = inquiryRecords.stream()
                .collect(Collectors.groupingBy(InquiryRecordDO::getLoanKey));
        for(Map.Entry<String, List<InquiryRecordDO>> entry : groupMap.entrySet()){
            List<InquiryRecordDO> records = entry.getValue();
            // 找出时间最大的
            records.stream().max(Comparator.comparing(InquiryRecordDO::getCreateTime)).ifPresent(latestRecord::add);
        }
        return latestRecord;
    }

    @Override
    public List<InquiryRecordVO> selectByLoanKey(String loanKey) {
        Map<String,String> apiLoanSourceMap = NacosClientAdapter.getMapConfig("api.loan.source.enum",String.class);
        List<ConclusionEnumVO> conclusionEnums = NacosClientAdapter.getListConfig("conclusion.enum", ConclusionEnumVO.class);
        Map<String,String> conclusionEnumMap = conclusionEnums.stream().collect(Collectors.toMap(ConclusionEnumVO::getCode,ConclusionEnumVO::getDesc));
        LambdaQueryWrapper<InquiryRecordDO> wrapper = new LambdaQueryWrapper<InquiryRecordDO>()
                .eq(InquiryRecordDO::getLoanKey, loanKey)
                .orderByDesc(InquiryRecordDO::getCreateTime);
        List<InquiryRecordDO> list = baseMapper.selectList(wrapper);
        return list.stream().map(item -> {
            String apiLoanSource = StringUtils.isBlank(item.getApiLoanSource()) ?
                    "HFQ" : item.getApiLoanSource();
            return new InquiryRecordVO()
                    .setUserKey(item.getUserKey())
                    .setLoanKey(item.getLoanKey())
                    .setHandler(item.getHandler())
                    .setApiLoanSource(apiLoanSource)
                    .setApiLoanSourceDesc(apiLoanSourceMap.get(apiLoanSource))
                    .setHandleTime(LocalDateTimeUtils.localDateTimeToString(item.getCreateTime()))
                    .setConclusionDesc(conclusionEnumMap.get(item.getConclusion()))
                    .setConclusionTypeDesc(ConclusionTypeEnum.valueOf(item.getConclusionType()).getDesc())
                    .setConclusionDetail(item.getConclusionDetail());
        }).collect(Collectors.toList());
    }

    @Override
    public InquiryRecordDO selectFinalOneByUserKey(String userKey) {
        LambdaQueryWrapper<InquiryRecordDO> wrapper = new LambdaQueryWrapper<InquiryRecordDO>()
                .eq(InquiryRecordDO::getUserKey, userKey)
                .eq(InquiryRecordDO::getConclusionType, "FINAL")
                .in(InquiryRecordDO::getConclusion, "FRAUD","OTHER_REJECT")
                .orderByDesc(InquiryRecordDO::getUpdateTime);
        List<InquiryRecordDO> inquiryRecordDOS = baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(inquiryRecordDOS)){
            return null;
        }
        return inquiryRecordDOS.get(0);
    }

}
