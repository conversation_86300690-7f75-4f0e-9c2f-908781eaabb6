package com.weicai.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.weicai.client.DpClient;
import com.weicai.client.VariableClient;
import com.weicai.entity.dto.*;
import com.weicai.entity.po.FraudCaseMainDO;
import com.weicai.entity.po.FraudCasePersonalDO;
import com.weicai.entity.po.InquiryRecordDO;
import com.weicai.entity.vo.*;
import com.weicai.enumeration.ConclusionTypeEnum;
import com.weicai.enumeration.FraudCaseStatusEnum;
import com.weicai.exception.AnalysisException;
import com.weicai.exception.RetCodeEnum;
import com.weicai.mapper.di.FraudCaseMainMapper;
import com.weicai.nacos.NacosClientAdapter;
import com.weicai.query.PageInfo;
import com.weicai.redis.RedisService;
import com.weicai.service.FraudCaseMainService;
import com.weicai.service.FraudCasePersonalService;
import com.weicai.service.InquiryRecordService;
import com.weicai.utils.LocalDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/20 16:23
 */
@Service
@Slf4j
public class FraudCaseMainServiceImpl extends ServiceImpl<FraudCaseMainMapper, FraudCaseMainDO> implements FraudCaseMainService {

    @Resource
    private InquiryRecordService inquiryRecordService;

    @Resource
    private RedisService redisService;

    @Resource
    private FraudCasePersonalService fraudCasePersonalService;

    @Resource
    private DpClient dpClient;

    @Resource
    private VariableClient variableClient;

    @Override
    public void saveCase(FraudCaseMainDO fraudCaseMainDO) {
        // 先查询
        FraudCaseMainDO existCase = getOne(new LambdaQueryWrapper<FraudCaseMainDO>()
                .eq(FraudCaseMainDO::getLoanKey, fraudCaseMainDO.getLoanKey()));
        if(existCase != null){
            // 更新jobId
            existCase.setJobId(fraudCaseMainDO.getJobId());
            existCase.setUpdateTime(LocalDateTime.now());
            updateById(existCase);
            // 如果案件已经结束，取审核结果直接回调
            if(existCase.getStatus() != null
                    && existCase.getStatus().equals(FraudCaseStatusEnum.FINISH.getValue())){
                log.info("FraudCase loanKey:{},already audit,re-callback",
                        existCase.getLoanKey());
                callbackDp(existCase.getLoanKey());
            }
            log.info("FraudCaseMainDO:{} exist，new jobId:{}", JSONObject.toJSONString(existCase),fraudCaseMainDO.getJobId());
            return;
        }
        // 查询拒绝原因
        String rejectRule = getRejectRule(fraudCaseMainDO.getLoanKey(), fraudCaseMainDO.getUserKey());
        if(StringUtils.isNotBlank(rejectRule)){
            fraudCaseMainDO.setRejectRule(rejectRule);
        }
        save(fraudCaseMainDO);
    }

    @Override
    public String getRejectRule(String loanKey, String userKey){
        try {
            JSONArray variableList = variableClient.queryVariable(userKey, loanKey,"rejectRulesForManualVarGroup",2);
            if(!variableList.isEmpty()){
                Map<String,String> variableMap = variableList.stream().collect(Collectors.toMap(item -> ((JSONObject) item).getString("code"),
                        item -> ((JSONObject) item).getString("value")));
                String rejectRules = variableMap.get("jg_lend_audit_reject_rules_for_manual");
                JSONObject rejectRulesJson = JSONObject.parseObject(rejectRules);
                return rejectRulesJson.keySet().stream().map(r -> r.split("_")[0]).collect(Collectors.joining(","));
            }
        }catch (Exception e){
            log.warn("拒绝原因转换异常：{}",e.getMessage());
        }
        return null;
    }

    @Override
    public PageInfo<PublicFraudCaseVO> pagePublicFraudCase(FraudCaseQueryDTO fraudCaseQueryDTO) {
        LambdaQueryWrapper<FraudCaseMainDO> wrapper = buildQueryWrapper(fraudCaseQueryDTO,"public");
        // 公共案件池只展示待处理案件
        wrapper.eq(FraudCaseMainDO::getStatus, FraudCaseStatusEnum.PENDING.getValue());
        IPage<FraudCaseMainDO> page = page(new Page<>(fraudCaseQueryDTO.getPageNum(),
                fraudCaseQueryDTO.getPageSize()), wrapper);
        List<String> loanKeys = page.getRecords()
                .stream().map(FraudCaseMainDO::getLoanKey).collect(Collectors.toList());
        // 获取待处理案件对应的处理记录
        List<InquiryRecordDO> inquiryRecordDOList = inquiryRecordService.selectByLoanKeys(loanKeys);
        Map<String, InquiryRecordDO> inquiryRecordDOMap = inquiryRecordDOList.stream()
                .collect(Collectors.toMap(InquiryRecordDO::getLoanKey, Function.identity()));
        // 转换为VO
        List<PublicFraudCaseVO> publicFraudCases = getPublicFraudCaseList(page.getRecords(), inquiryRecordDOMap);
        PageInfo<PublicFraudCaseVO> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(fraudCaseQueryDTO.getPageNum());
        pageInfo.setPageSize(fraudCaseQueryDTO.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setList(publicFraudCases);
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FraudCaseApplyVO applyCase(FraudCaseApplyDTO fraudCaseApplyDTO) {
        FraudCaseApplyVO result = new FraudCaseApplyVO();
        boolean locked = redisService.lock("fraudCaseApplyLock","1",5, TimeUnit.SECONDS);
        try {
            if (!locked) {
                throw new AnalysisException(RetCodeEnum.ERROR, "当前有用户在申请(分配)案件，稍后重试");
            }
            // 优先获取案件信息
            List<FraudCaseMainDO> fraudCaseMainDOList = beforeOperateCase(fraudCaseApplyDTO.getLoanKeys());
            // 判断有哪些案件未被领取
            List<FraudCaseMainDO> unreceivedCaseList = fraudCaseMainDOList.stream()
                    .filter(fraudCaseMainDO -> Objects.equals(fraudCaseMainDO.getStatus(), FraudCaseStatusEnum.PENDING.getValue()))
                    .collect(Collectors.toList());
            result.setUnSuccessCaseNum(fraudCaseApplyDTO.getLoanKeys().size() - unreceivedCaseList.size());
            // 未被领取的案件写入个人案件表
            List<FraudCasePersonalDO> fraudCasePersonalDOList = unreceivedCaseList.stream().map(fraudCaseMainDO -> new FraudCasePersonalDO()
                    .setLoanKey(fraudCaseMainDO.getLoanKey())
                    .setUserKey(fraudCaseMainDO.getUserKey())
                    .setLoanTime(fraudCaseMainDO.getLoanTime())
                    .setHandler(fraudCaseApplyDTO.getHandler())
                    .setApiLoanSource(fraudCaseMainDO.getApiLoanSource())
                    .setCreateTime(LocalDateTime.now())
                    .setUpdateTime(LocalDateTime.now())).collect(Collectors.toList());
            if(!fraudCasePersonalDOList.isEmpty()) {
                fraudCasePersonalService.saveBatch(fraudCasePersonalDOList);
                // 更新主表案件状态为审核中
                unreceivedCaseList.forEach(fraudCaseMainDO -> {
                    fraudCaseMainDO.setStatus(FraudCaseStatusEnum.AUDITING.getValue());
                    fraudCaseMainDO.setHandler(fraudCaseApplyDTO.getHandler());
                });
                updateBatchById(unreceivedCaseList);
            }
            result.setSuccessCaseNum(fraudCasePersonalDOList.size());
        }finally {
            redisService.unlock("fraudCaseApplyLock","1");
        }
        return result;
    }

    private List<FraudCaseMainDO> beforeOperateCase(List<String> loanKeys){
        // 优先获取案件信息
        LambdaQueryWrapper<FraudCaseMainDO> mainWrapper = new LambdaQueryWrapper<FraudCaseMainDO>()
                .in(FraudCaseMainDO::getLoanKey, loanKeys);
        List<FraudCaseMainDO> fraudCaseMainDOList = list(mainWrapper);
        if(fraudCaseMainDOList.isEmpty()
                || fraudCaseMainDOList.size() != loanKeys.size()){
            throw new AnalysisException(RetCodeEnum.ERROR, "案件不存在");
        }
        long finishCaseNum = fraudCaseMainDOList.stream().filter(fraudCaseMainDO
                -> Objects.equals(fraudCaseMainDO.getStatus(), FraudCaseStatusEnum.FINISH.getValue())).count();
        if(finishCaseNum > 0){
            throw new AnalysisException(RetCodeEnum.ERROR, "案件已处理完成，无法再次分配（领取）");
        }
        return fraudCaseMainDOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FraudCaseAllocationVO allocationCase(FraudCaseAllocationDTO fraudCaseAllocationDTO) {
        FraudCaseAllocationVO result = new FraudCaseAllocationVO();
        boolean locked = redisService.lock("fraudCaseApplyLock","1",5, TimeUnit.SECONDS);
        try {
            if(!locked){
                throw new AnalysisException(RetCodeEnum.ERROR, "当前有用户在申请(分配)案件，稍后重试");
            }
            // 优先获取案件信息
            List<FraudCaseMainDO> fraudCaseMainDOList = beforeOperateCase(fraudCaseAllocationDTO.getLoanKeys());
            // 删除个人案件表
            fraudCasePersonalService.remove(new LambdaQueryWrapper<FraudCasePersonalDO>()
                    .in(FraudCasePersonalDO::getLoanKey, fraudCaseAllocationDTO.getLoanKeys()));
            // 案件写入个人表
            List<FraudCasePersonalDO> fraudCasePersonalDOList = fraudCaseMainDOList.stream().map(fraudCaseMainDO -> new FraudCasePersonalDO()
                    .setLoanKey(fraudCaseMainDO.getLoanKey())
                    .setUserKey(fraudCaseMainDO.getUserKey())
                    .setLoanTime(fraudCaseMainDO.getLoanTime())
                    .setHandler(fraudCaseAllocationDTO.getHandler())
                    .setApiLoanSource(fraudCaseMainDO.getApiLoanSource())
                    .setCreateTime(LocalDateTime.now())
                    .setUpdateTime(LocalDateTime.now())).collect(Collectors.toList());
            fraudCasePersonalService.saveBatch(fraudCasePersonalDOList);
            // 更新主表案件状态为审核中
            fraudCaseMainDOList.forEach(fraudCaseMainDO -> {
                fraudCaseMainDO.setStatus(FraudCaseStatusEnum.AUDITING.getValue());
                fraudCaseMainDO.setHandler(fraudCaseAllocationDTO.getHandler());
            });
            updateBatchById(fraudCaseMainDOList);
            result.setCount(fraudCasePersonalDOList.size());
        } finally {
            redisService.unlock("fraudCaseApplyLock","1");
        }
        return result;
    }

    @Override
    public PageInfo<MainFraudCaseVO> pageMainFraudCase(FraudCaseQueryDTO fraudCaseQueryDTO) {
        LambdaQueryWrapper<FraudCaseMainDO> wrapper = buildQueryWrapper(fraudCaseQueryDTO, "main");
        IPage<FraudCaseMainDO> page = page(new Page<>(fraudCaseQueryDTO.getPageNum(),
                fraudCaseQueryDTO.getPageSize()), wrapper);
        // 过滤出审核中还有审核完成的案件
        List<FraudCaseMainDO> fraudCaseMainDOList = page.getRecords().stream()
                .filter(fraudCaseMainDO -> Objects.equals(fraudCaseMainDO.getStatus(), FraudCaseStatusEnum.AUDITING.getValue())
                        || Objects.equals(fraudCaseMainDO.getStatus(), FraudCaseStatusEnum.FINISH.getValue()))
                .collect(Collectors.toList());
        List<String> loanKeys = fraudCaseMainDOList.stream().map(FraudCaseMainDO::getLoanKey).collect(Collectors.toList());
        // 获取审核中还有审核完成案件对应的处理记录
        List<InquiryRecordDO> inquiryRecordDOList = inquiryRecordService.selectByLoanKeys(loanKeys);
        Map<String, InquiryRecordDO> inquiryRecordDOMap = inquiryRecordDOList.stream()
                .collect(Collectors.toMap(InquiryRecordDO::getLoanKey, Function.identity()));
        List<MainFraudCaseVO> mainFraudCases = getMainFraudCaseList(page.getRecords(),inquiryRecordDOMap);
        PageInfo<MainFraudCaseVO> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(fraudCaseQueryDTO.getPageNum());
        pageInfo.setPageSize(fraudCaseQueryDTO.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setList(mainFraudCases);
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitConclusion(FraudCaseConclusionDTO fraudCaseConclusionDTO) {
        // 检查案件是否已经结束
        LambdaQueryWrapper<FraudCaseMainDO> wrapper = new LambdaQueryWrapper<FraudCaseMainDO>()
                .eq(FraudCaseMainDO::getLoanKey, fraudCaseConclusionDTO.getLoanKey());
        FraudCaseMainDO fraudCaseMainDO = getOne(wrapper);
        if(fraudCaseMainDO.getStatus().equals(FraudCaseStatusEnum.FINISH.getValue())){
            throw new AnalysisException(RetCodeEnum.ERROR, "案件已处理完成，无法再次提交结论");
        }
        if(ConclusionTypeEnum.FINAL.name().equals(fraudCaseConclusionDTO.getConclusionType())
                && fraudCaseConclusionDTO.getAuditResult() == null){
            throw new AnalysisException(RetCodeEnum.ERROR, "最终结论审核结果不能为空");
        }
        // 下结论时需要判断该案件是否还在自己名下
        if(fraudCaseConclusionDTO.getOverTimeAutoSubmit() == null
                || !fraudCaseConclusionDTO.getOverTimeAutoSubmit()) {
            LambdaQueryWrapper<FraudCasePersonalDO> personalWrapper = new LambdaQueryWrapper<FraudCasePersonalDO>()
                    .eq(FraudCasePersonalDO::getLoanKey, fraudCaseConclusionDTO.getLoanKey());
            FraudCasePersonalDO fraudCasePersonalDO = fraudCasePersonalService.getOne(personalWrapper);
            if (!Objects.equals(fraudCaseConclusionDTO.getHandler(), fraudCasePersonalDO.getHandler())) {
                throw new AnalysisException(RetCodeEnum.ERROR, "案件不在自己名下，无法提交结论");
            }
        }
        // 写入审核记录
        InquiryRecordDO inquiryRecordDO = new InquiryRecordDO()
                .setConclusion(fraudCaseConclusionDTO.getConclusion())
                .setConclusionDetail(fraudCaseConclusionDTO.getConclusionDetail())
                .setConclusionType(fraudCaseConclusionDTO.getConclusionType())
                .setApiLoanSource(fraudCaseMainDO.getApiLoanSource())
                .setHandler(fraudCaseConclusionDTO.getHandler())
                .setLoanKey(fraudCaseConclusionDTO.getLoanKey())
                .setUserKey(fraudCaseConclusionDTO.getUserKey())
                .setAuditResult(fraudCaseConclusionDTO.getAuditResult());
        inquiryRecordService.save(inquiryRecordDO);
        // 更新主表
        LambdaUpdateWrapper<FraudCaseMainDO> mainUpdateWrapper = new LambdaUpdateWrapper<FraudCaseMainDO>()
                .eq(FraudCaseMainDO::getLoanKey, fraudCaseConclusionDTO.getLoanKey())
                .set(FraudCaseMainDO::getHandler, fraudCaseConclusionDTO.getHandler())
                .set(FraudCaseMainDO::getHandleTime, LocalDateTime.now());
        if(ConclusionTypeEnum.FINAL.name().equals(fraudCaseConclusionDTO.getConclusionType())){
            mainUpdateWrapper.set(FraudCaseMainDO::getAuditResult, fraudCaseConclusionDTO.getAuditResult())
                    .set(FraudCaseMainDO::getStatus, FraudCaseStatusEnum.FINISH.getValue());
        }
        update(mainUpdateWrapper);
        if(ConclusionTypeEnum.FINAL.name().equals(fraudCaseConclusionDTO.getConclusionType())){
            // 最终结论案件结束删除个人表
            fraudCasePersonalService.remove(new LambdaQueryWrapper<FraudCasePersonalDO>()
                    .eq(FraudCasePersonalDO::getLoanKey, fraudCaseConclusionDTO.getLoanKey()));
            // 最终结论回调dp
            callbackDp(fraudCaseConclusionDTO.getLoanKey());
        }
    }

    @Override
    public void callbackDp(String loanKey) {
        LambdaQueryWrapper<FraudCaseMainDO> wrapper = new LambdaQueryWrapper<FraudCaseMainDO>()
                .eq(FraudCaseMainDO::getLoanKey, loanKey);
        FraudCaseMainDO fraudCaseMainDO = getOne(wrapper);
        FraudCaseCallbackDTO fraudCaseCallbackDTO = new FraudCaseCallbackDTO()
                .setUserKey(fraudCaseMainDO.getUserKey())
                .setLoanKey(fraudCaseMainDO.getLoanKey())
                .setJobId(fraudCaseMainDO.getJobId())
                .setSystemEventCode(fraudCaseMainDO.getEventCode())
                .setVerifyResult(fraudCaseMainDO.getAuditResult() == 0 ? "REJECT" : "ACCEPT");
        dpClient.callbackDp(fraudCaseCallbackDTO);
    }

    @Override
    public FraudCaseResultVO queryCaseResult(String loanKey) {
        if(StringUtils.isBlank(loanKey)){
            throw new AnalysisException(RetCodeEnum.ERROR, "loanKey必须");
        }
        LambdaQueryWrapper<FraudCaseMainDO> wrapper = new LambdaQueryWrapper<FraudCaseMainDO>()
                .eq(FraudCaseMainDO::getLoanKey, loanKey);
        FraudCaseMainDO fraudCaseMainDO = getOne(wrapper);
        if(!fraudCaseMainDO.getStatus().equals(FraudCaseStatusEnum.FINISH.getValue())){
            throw new AnalysisException(RetCodeEnum.ERROR, "案件未审批结束，无法获取结果");
        }
        if(fraudCaseMainDO.getAuditResult() != null){
            FraudCaseResultVO fraudCaseResultVO = new FraudCaseResultVO();
            fraudCaseResultVO.setUserKey(fraudCaseMainDO.getUserKey());
            fraudCaseResultVO.setLoanKey(fraudCaseMainDO.getLoanKey());
            fraudCaseResultVO.setAuditResult(fraudCaseMainDO.getAuditResult());
            return fraudCaseResultVO;
        }
        return null;
    }

    @Override
    public List<String> getAllUnfinishedUserKeys() {
        LambdaQueryWrapper<FraudCaseMainDO> wrapper = new LambdaQueryWrapper<FraudCaseMainDO>()
                .in(FraudCaseMainDO::getStatus, Lists.newArrayList(FraudCaseStatusEnum.PENDING.getValue()
                        ,FraudCaseStatusEnum.AUDITING.getValue()));
        return list(wrapper).stream().map(FraudCaseMainDO::getUserKey).collect(Collectors.toList());
    }

    @Override
    public List<String> getAllOverTimeCase(String time) {
        LambdaQueryWrapper<FraudCaseMainDO> wrapper = new LambdaQueryWrapper<FraudCaseMainDO>()
                .le(FraudCaseMainDO::getLoanTime, time)
                .in(FraudCaseMainDO::getStatus, Lists.newArrayList(FraudCaseStatusEnum.PENDING.getValue()
                        ,FraudCaseStatusEnum.AUDITING.getValue()));
        return list(wrapper).stream().map(FraudCaseMainDO::getLoanKey).collect(Collectors.toList());
    }

    private List<MainFraudCaseVO> getMainFraudCaseList(List<FraudCaseMainDO> fraudCaseMainDOList,Map<String, InquiryRecordDO> inquiryRecordDOMap) {
        List<MainFraudCaseVO> mainFraudCases = new ArrayList<>();
        Map<String,String> apiLoanSourceMap = NacosClientAdapter.getMapConfig("api.loan.source.enum",String.class);
        List<ConclusionEnumVO> conclusionEnums = NacosClientAdapter.getListConfig("conclusion.enum", ConclusionEnumVO.class);
        Map<String,String> conclusionEnumMap = conclusionEnums.stream().collect(Collectors.toMap(ConclusionEnumVO::getCode,ConclusionEnumVO::getDesc));
        for(FraudCaseMainDO fraudCaseMainDO : fraudCaseMainDOList){
            MainFraudCaseVO mainFraudCaseVO = new MainFraudCaseVO();
            mainFraudCaseVO.setLoanKey(fraudCaseMainDO.getLoanKey());
            mainFraudCaseVO.setLoanTimeFormat(LocalDateTimeUtils.localDateTimeToString(fraudCaseMainDO.getLoanTime()));
            mainFraudCaseVO.setUserKey(fraudCaseMainDO.getUserKey());
            mainFraudCaseVO.setStatus(fraudCaseMainDO.getStatus());
            mainFraudCaseVO.setStatusDesc(FraudCaseStatusEnum.getByCode(fraudCaseMainDO.getStatus())
                    .map(FraudCaseStatusEnum::getDesc).orElse(""));
            String apiLoanSource = StringUtils.isBlank(fraudCaseMainDO.getApiLoanSource()) ?
                    "HFQ" : fraudCaseMainDO.getApiLoanSource();
            mainFraudCaseVO.setApiLoanSource(apiLoanSource);
            mainFraudCaseVO.setApiLoanSourceDesc(apiLoanSourceMap.get(apiLoanSource));
            mainFraudCaseVO.setRejectRule(fraudCaseMainDO.getRejectRule());
            if(fraudCaseMainDO.getAuditResult() != null) {
                mainFraudCaseVO.setAuditResult(fraudCaseMainDO.getAuditResult());
                mainFraudCaseVO.setAuditResultDesc(fraudCaseMainDO.getAuditResult() == 1 ? "是" : "否");
            }
            mainFraudCaseVO.setHandler(fraudCaseMainDO.getHandler());
            mainFraudCaseVO.setHandlerTime(LocalDateTimeUtils.localDateTimeToString(fraudCaseMainDO.getHandleTime()));
            // 结论和结论详情另一张表获取
            InquiryRecordDO inquiryRecordDO = inquiryRecordDOMap.get(fraudCaseMainDO.getLoanKey());
            if(inquiryRecordDO != null){
                mainFraudCaseVO.setConclusionDesc(conclusionEnumMap.get(inquiryRecordDO.getConclusion()));
                mainFraudCaseVO.setConclusionDetail(inquiryRecordDO.getConclusionDetail());
                mainFraudCaseVO.setConclusionTypeDesc(ConclusionTypeEnum.valueOf(inquiryRecordDO.getConclusionType()).getDesc());
            }
            mainFraudCases.add(mainFraudCaseVO);
        }
        return mainFraudCases;
    }

    private List<PublicFraudCaseVO> getPublicFraudCaseList(List<FraudCaseMainDO> fraudCaseMainDOList,Map<String, InquiryRecordDO> inquiryRecordDOMap) {
        List<PublicFraudCaseVO> publicFraudCases = new ArrayList<>();
        Map<String,String> apiLoanSourceMap = NacosClientAdapter.getMapConfig("api.loan.source.enum",String.class);
        for(FraudCaseMainDO fraudCaseMainDO : fraudCaseMainDOList){
            InquiryRecordDO inquiryRecordDO = inquiryRecordDOMap.get(fraudCaseMainDO.getLoanKey());
            PublicFraudCaseVO publicFraudCaseVO = new PublicFraudCaseVO();
            publicFraudCaseVO.setLoanKey(fraudCaseMainDO.getLoanKey());
            publicFraudCaseVO.setLoanTimeFormat(LocalDateTimeUtils.localDateTimeToString(fraudCaseMainDO.getLoanTime()));
            publicFraudCaseVO.setUserKey(fraudCaseMainDO.getUserKey());
            String apiLoanSource = StringUtils.isBlank(fraudCaseMainDO.getApiLoanSource()) ?
                    "HFQ" : fraudCaseMainDO.getApiLoanSource();
            publicFraudCaseVO.setApiLoanSource(apiLoanSource);
            publicFraudCaseVO.setApiLoanSourceDesc(apiLoanSourceMap.get(apiLoanSource));
            if(inquiryRecordDO != null){
                publicFraudCaseVO.setLastHandlerName(inquiryRecordDO.getHandler());
                publicFraudCaseVO.setLastHandlerDetail(inquiryRecordDO.getConclusionDetail());
                publicFraudCaseVO.setLastHandlerTypeDesc(ConclusionTypeEnum.valueOf(inquiryRecordDO.getConclusionType()).getDesc());
                publicFraudCaseVO.setLastHandlerTime(LocalDateTimeUtils.localDateTimeToString(inquiryRecordDO.getUpdateTime()));
            }
            publicFraudCases.add(publicFraudCaseVO);
        }
        return publicFraudCases;
    }

    private LambdaQueryWrapper<FraudCaseMainDO> buildQueryWrapper(FraudCaseQueryDTO fraudCaseQueryDTO,String type) {
        LambdaQueryWrapper<FraudCaseMainDO> wrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotBlank(fraudCaseQueryDTO.getUserKey())){
            wrapper.eq(FraudCaseMainDO::getUserKey,fraudCaseQueryDTO.getUserKey());
        }
        if(StringUtils.isNotBlank(fraudCaseQueryDTO.getLoanKey())){
            wrapper.eq(FraudCaseMainDO::getLoanKey,fraudCaseQueryDTO.getLoanKey());
        }
        if(StringUtils.isNotBlank(fraudCaseQueryDTO.getApiLoanSource())){
            wrapper.eq(FraudCaseMainDO::getApiLoanSource,fraudCaseQueryDTO.getApiLoanSource());
        }
        if (StringUtils.isNotBlank(fraudCaseQueryDTO.getLoanTimeStart())){
            wrapper.ge(FraudCaseMainDO::getLoanTime,fraudCaseQueryDTO.getLoanTimeStart());
        }
        if (StringUtils.isNotBlank(fraudCaseQueryDTO.getLoanTimeEnd())){
            wrapper.lt(FraudCaseMainDO::getLoanTime,fraudCaseQueryDTO.getLoanTimeEnd());
        }
        if(fraudCaseQueryDTO.getStatus() != null){
            wrapper.eq(FraudCaseMainDO::getStatus,fraudCaseQueryDTO.getStatus());
        }
        if (StringUtils.isNotBlank(fraudCaseQueryDTO.getHandler())){
            wrapper.eq(FraudCaseMainDO::getHandler,fraudCaseQueryDTO.getHandler());
        }
        if(StringUtils.isNotBlank(fraudCaseQueryDTO.getHandleTimeStart())){
            wrapper.ge(FraudCaseMainDO::getHandleTime,fraudCaseQueryDTO.getHandleTimeStart());
        }
        if(StringUtils.isNotBlank(fraudCaseQueryDTO.getHandleTimeEnd())){
            wrapper.lt(FraudCaseMainDO::getHandleTime,fraudCaseQueryDTO.getHandleTimeEnd());
        }
        if(StringUtils.isNotBlank(fraudCaseQueryDTO.getRejectRule())){
            wrapper.like(FraudCaseMainDO::getRejectRule,fraudCaseQueryDTO.getRejectRule());
        }
        if(fraudCaseQueryDTO.getIsPass() != null){
            switch (fraudCaseQueryDTO.getIsPass()){
                case 0:
                case 1:
                    wrapper.eq(FraudCaseMainDO::getAuditResult,fraudCaseQueryDTO.getIsPass());
                    break;
                case 2:
                    wrapper.isNull(FraudCaseMainDO::getAuditResult);
                    break;
                default:
                    throw new IllegalArgumentException("isPass参数错误");
            }
        }
        if("main".equals(type)){
            wrapper.orderByDesc(FraudCaseMainDO::getLoanTime);
        }else {
            wrapper.orderByAsc(FraudCaseMainDO::getLoanTime);
        }
        return wrapper;
    }

}
