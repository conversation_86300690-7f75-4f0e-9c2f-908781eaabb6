package com.weicai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weicai.entity.dto.NetworkRelatedNodeRecordQueryDTO;
import com.weicai.entity.po.RaNetworkRelatedNodeRecordDO;
import com.weicai.query.PageInfo;

/**
 * <AUTHOR>
 * @description: TODO
 * Date: 2025/5/26
 */
public interface NetworkRelateNodeRecordService extends IService<RaNetworkRelatedNodeRecordDO> {


    PageInfo<RaNetworkRelatedNodeRecordDO> queryRelatedNodeRecordPageList(NetworkRelatedNodeRecordQueryDTO param);

    /**
     * 查询关联记录数
     * @param nodeId
     * @return
     */
    Integer queryCount(String nodeId);
}
