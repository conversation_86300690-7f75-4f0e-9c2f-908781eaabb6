package com.weicai.service;

/**
 * 人工审核状态查询服务接口
 * 
 * <AUTHOR>
 * @since 2024/12/26
 */
public interface ManualAuditStatusService {
    
    /**
     * 查询用户是否正在人工审核中
     * 
     * @param userKey 用户标识
     * @return true-正在审核中，false-未在审核中
     */
    boolean isInManualAudit(String userKey);
    
    /**
     * 手动设置用户进入审核状态
     * 
     * @param userKey 用户标识
     * @param expireSeconds 过期时间（秒）
     */
    void enterAuditStatus(String userKey, long expireSeconds);
    
    /**
     * 手动设置用户退出审核状态
     *
     * @param userKey 用户标识
     */
    void exitAuditStatus(String userKey);

    /**
     * 批量设置用户进入审核状态
     *
     * @param userKeys 用户标识列表
     * @param expireSeconds 过期时间（秒）
     */
    void batchEnterAuditStatus(java.util.List<String> userKeys, long expireSeconds);

    /**
     * 批量设置用户退出审核状态
     *
     * @param userKeys 用户标识列表
     */
    void batchExitAuditStatus(java.util.List<String> userKeys);
}
