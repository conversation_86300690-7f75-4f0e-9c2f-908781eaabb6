package com.weicai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weicai.entity.dto.NetworkNodeTagQueryDTO;
import com.weicai.entity.po.RaNetworkNodeTagDO;
import com.weicai.entity.vo.NetworkNodeTagListVO;
import com.weicai.query.PageInfo;

/**
 * <AUTHOR>
 * @description: TODO
 * Date: 2025/5/26
 */
public interface NetworkNodeTagService extends IService<RaNetworkNodeTagDO> {

    PageInfo<NetworkNodeTagListVO> queryNodeTagList(NetworkNodeTagQueryDTO param);

    RaNetworkNodeTagDO queryNodeTagLastOne(NetworkNodeTagQueryDTO param);
}
