package com.weicai.service;

import com.weicai.entity.dto.*;
import com.weicai.entity.po.RaNetworkNodeTagDO;
import com.weicai.entity.po.RaNetworkRelatedNodeRecordDO;
import com.weicai.entity.vo.NetworkNodeTagListVO;
import com.weicai.entity.vo.NetworkNodeVO;
import com.weicai.entity.vo.NetworkRelatedUserPageVO;
import com.weicai.query.PageInfo;

/**
 * <AUTHOR>
 * @description: TODO
 * Date: 2025/5/22
 */
public interface NetworkService {
    /**
     * 关联网络主视图查询
     *
     * @param param
     * @return
     */
    NetworkNodeVO queryNodeNetwork(NetworkNodeQueryDTO param);

    /**
     * 节点关联用户查询
     * @param param
     * @return
     */
    PageInfo<NetworkRelatedUserPageVO> queryRelatedNodePageList(NetworkRelatedUserQueryDTO param);

    /**
     * 节点标签新增
     * @param param
     */
    void addNodeTag(NetworkNodeTagAddDTO param);

    /**
     * 节点标签查询
     * @param param
     * @return
     */
    PageInfo<NetworkNodeTagListVO> queryNodeTagList(NetworkNodeTagQueryDTO param);

    /**
     * 关联用户反欺诈记录列表查询
     * @param param
     * @return
     */
    PageInfo<RaNetworkRelatedNodeRecordDO> queryRelatedNodeRecordPageList(NetworkRelatedNodeRecordQueryDTO param);

    /**
     * 新增关联节点反欺诈记录
     * @param param
     */
    void addRelatedNodeRecord(NetworkRelatedNodeRecordAddDTO param);

    /**
     * 查询最新节点标签
     * @param param
     * @return
     */
    RaNetworkNodeTagDO queryNodeTagLastOne(NetworkNodeTagQueryDTO param);
}
