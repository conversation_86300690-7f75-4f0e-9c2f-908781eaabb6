package com.weicai.utils;

import com.weicai.nacos.NacosClientAdapter;
import org.opencv.core.Point;
import org.opencv.core.*;
import org.opencv.dnn.Dnn;
import org.opencv.dnn.Net;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;
import origami.Origami;

import javax.annotation.PostConstruct;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Properties;

/**
 * opencv人脸识别后打码
 *
 * <AUTHOR>
 * @since 2024/1/30 10:43
 */
@Component
public class ImageMasking {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImageMasking.class);

    /**
     * 基于DNN的人脸识别
     */
    private Net net;

    @PostConstruct
    public void init() throws FileNotFoundException {
        // 加载库文件
        Origami.init();
        // 创建级联分类器加载人脸模型xml文件
        Properties props = System.getProperties();
        String osName = props.getProperty("os.name");
        if(osName.startsWith("Window")) {
            File deploy = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "opencv/deploy.prototxt");
            String deployPath = deploy.getPath();
            File res10 = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "opencv/res10_300x300_ssd_iter_140000_fp16.caffemodel");
            String res10Path = res10.getPath();
            net = Dnn.readNetFromCaffe(deployPath,res10Path);
        }else {
            // 其他环境把模型文件放到指定位置
            net = Dnn.readNetFromCaffe("/opt/app/deploy.prototxt","/opt/app/res10_300x300_ssd_iter_140000_fp16.caffemodel");
        }
        LOGGER.info("ImageMasking model,osName:{},",osName);
    }

    public byte[] detectFaceAndMask(byte[] imageBytes,String userKey){
        try{
            Mat image = Imgcodecs.imdecode(new MatOfByte(imageBytes),Imgcodecs.IMREAD_COLOR);
            //为了获得最佳精度，必须分别对蓝色、绿色和红色通道执行 `(104, 177, 123)` 通道均值减法
            Mat inputBlob;
            if(image.size().width > 1000 || image.size().height > 1000){
                // 把分辨率调整为1000以内
                double width = image.size().width;double height = image.size().height;
                double base = Math.max(width, height);
                double factor = 1000 / base;
                inputBlob = Dnn.blobFromImage(image, 1.0f,
                        new Size(width * factor, height * factor),
                        new Scalar(104, 117, 123), false, false);
            }else {
                inputBlob = Dnn.blobFromImage(image, 1.0f,
                        new Size(image.size().width, image.size().height),
                        new Scalar(104, 117, 123), false, false);
            }
            net.setInput(inputBlob);
            Mat res = net.forward();
            Mat faces = res.reshape(1, res.size(2));
            float [] data = new float[7];
            // 创建一个和原图一样大小的全黑图像，用于记录人脸位置
            Mat mask = Mat.zeros(image.size(), image.type());
            for (int i = 0; i < faces.rows(); i++)
            {
                faces.get(i, 0, data);
                float confidence = data[2];
                float faceConfidence = NacosClientAdapter.getConfig("face.confidence", Float.class, 0.5f);
                if (confidence > faceConfidence)
                {
                    int left   = (int)(data[3] * image.cols());
                    int top    = (int)(data[4] * image.rows());
                    int right  = (int)(data[5] * image.cols());
                    int bottom = (int)(data[6] * image.rows());
                    LOGGER.info("人脸检测结果,检测到人脸指数大于阈值,userKey:{}",userKey);
                    Imgproc.rectangle(mask, new Point(left,top), new Point(right,bottom), new Scalar(255,255,255), -1);
                }
            }
            // 对输入图像进行模糊处理得到模糊图像
            Mat blurredImage = new Mat();
            Imgproc.GaussianBlur(image, blurredImage, new Size(111, 111), 0);

            // 使用掩码将原始图像的人脸区域复制到模糊图像上，使人脸区域保持清晰
            image.copyTo(blurredImage, mask);
            MatOfByte matOfByte = new MatOfByte();
            Imgcodecs.imencode(".jpg", blurredImage, matOfByte);
            return matOfByte.toArray();
        }catch (Exception e){
            LOGGER.error("人脸识别失败,userKey:{}",userKey,e);
            throw new RuntimeException("人脸识别失败");
        }
    }

    public byte[] addTextWatermark(byte[] inputImageBytes, String text) {
        try (ByteArrayInputStream bais = new ByteArrayInputStream(inputImageBytes);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {

            BufferedImage sourceImage = ImageIO.read(bais);
            if (sourceImage == null) {
                throw new IOException("Unsupported image format");
            }

            Graphics2D g2d = sourceImage.createGraphics();
            configureGraphics(g2d, sourceImage);
            drawTextWatermark(sourceImage, g2d, text);
            g2d.dispose();

            ImageIO.write(sourceImage, "png", baos);
            return baos.toByteArray();
        }catch (Exception e){
            LOGGER.error("添加文字水印失败",e);
        }
        return inputImageBytes;
    }

    private static void configureGraphics(Graphics2D g2d, BufferedImage image) {
        // 根据图片尺寸计算基础字号（宽度5%）
        int baseSize = (int) (image.getWidth() * 0.05);
        Font font = new Font("微软雅黑", Font.BOLD, Math.max(24, baseSize));
        g2d.setFont(font);

        // 设置半透明效果
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.6f));
        g2d.setColor(new Color(255, 255, 255, 200));

        // 抗锯齿设置
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING,
                RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,
                RenderingHints.VALUE_TEXT_ANTIALIAS_LCD_HRGB);
    }

    private static void drawTextWatermark(BufferedImage image, Graphics2D g2d, String text) {
        FontMetrics metrics = g2d.getFontMetrics();
        Rectangle2D bounds = metrics.getStringBounds(text, g2d);

        // 计算居中坐标
        int x = (image.getWidth() - (int) bounds.getWidth()) / 2;
        int y = (image.getHeight() - metrics.getHeight()) / 2 + metrics.getAscent();

        // 添加文字阴影增强可读性
        g2d.setColor(new Color(0, 0, 0, 100));
        g2d.drawString(text, x + 2, y + 2);

        g2d.setColor(new Color(255, 255, 255, 200));
        g2d.drawString(text, x, y);
    }

    public static void main(String[] args) {
        Origami.init();
        Net net = Dnn.readNetFromCaffe("C:\\Users\\<USER>\\Desktop\\deploy.prototxt", "C:\\Users\\<USER>\\Desktop\\res10_300x300_ssd_iter_140000_fp16.caffemodel");

        String path = "";
//        try {
//            File file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "opencv/haarcascade_frontalface_alt.xml");
//            path = file.getPath();
//        } catch (FileNotFoundException e) {
//            LOGGER.error("model file not find", e);
//        }
//        CascadeClassifier faceDetector = new CascadeClassifier(path);
        Mat image = Imgcodecs.imread("C:/Users/<USER>/Desktop/uFile.jpg");

       //为了获得最佳精度，必须分别对蓝色、绿色和红色通道执行 `(104, 177, 123)` 通道均值减法
        Mat inputBlob;
        if(image.size().width > 1000 || image.size().height > 1000){
            // 把分辨率调整为100以内
            double width = image.size().width;double height = image.size().height;
            double base = Math.max(width, height);
            double factor = 1000 / base;
            inputBlob = Dnn.blobFromImage(image, 1.0f,
                    new Size(width * factor, height * factor),
                    new Scalar(104, 117, 123), false, false);
        }else {
            inputBlob = Dnn.blobFromImage(image, 1.0f,
                    new Size(image.size().width, image.size().height),
                    new Scalar(104, 117, 123), false, false);
        }
        net.setInput(inputBlob);
        Mat res = net.forward();
        Mat faces = res.reshape(1, res.size(2));
        System.out.println("faces" + faces.rows());
        float [] data = new float[7];
        // 创建一个和原图一样大小的全黑图像，用于记录人脸位置
        Mat mask = Mat.zeros(image.size(), image.type());
        for (int i=0; i<faces.rows(); i++)
        {
            faces.get(i, 0, data);
            float confidence = data[2];
            if (confidence > 0.5f)
            {
                int left   = (int)(data[3] * image.cols());
                int top    = (int)(data[4] * image.rows());
                int right  = (int)(data[5] * image.cols());
                int bottom = (int)(data[6] * image.rows());
                System.out.println("("+left + "," + top + ")("+right+","+bottom+") " + confidence);
                Imgproc.rectangle(mask, new Point(left,top), new Point(right,bottom), new Scalar(255,255,255), -1);
            }
        }
        // 对输入图像进行模糊处理得到模糊图像
        Mat blurredImage = new Mat();
        Imgproc.GaussianBlur(image, blurredImage, new Size(111, 111), 0);

        // 使用掩码将原始图像的人脸区域复制到模糊图像上，使人脸区域保持清晰
        image.copyTo(blurredImage, mask);

        //生成的图片名字
        Imgcodecs.imwrite("C:\\Users\\<USER>\\Desktop\\result.jpg", blurredImage);

//        // 转为灰度图像
//        Mat gray = new Mat();
//        Imgproc.cvtColor(image,gray,Imgproc.COLOR_BGR2GRAY);
//
//        // 检测人脸
//        MatOfRect faceDetections = new MatOfRect();
//        faceDetector.detectMultiScale(gray, faceDetections);
//
//        int numFaces = faceDetections.toArray().length;
//        System.out.println("人脸数量: " + numFaces);
//        // 创建一个和原图一样大小的全黑图像，用于记录人脸位置
//        Mat mask = Mat.zeros(image.size(), image.type());
//        for (Rect rect : faceDetections.toArray()) {
//            // 在mask上白色绘制人脸区域，之后这部分区域将不会被打码
//            Imgproc.rectangle(mask, new Point(rect.x , rect.y),
//                    new Point(rect.x + rect.width, rect.y + rect.height),
//                    new Scalar(255, 255, 255), -1);
//        }
//        // 对输入图像进行模糊处理得到模糊图像
//        Mat blurredImage = new Mat();
//        Imgproc.GaussianBlur(image, blurredImage, new Size(111, 111), 0);
//
//        // 使用掩码将原始图像的人脸区域复制到模糊图像上，使人脸区域保持清晰
//        image.copyTo(blurredImage, mask);
//
//        // 保存打码后的图片
//        Imgcodecs.imwrite("C:\\Users\\<USER>\\Desktop\\result.jpg", blurredImage);
    }
}
