package com.weicai.utils;

import com.weicai.query.PageInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 内存分页
 */
public class PageUtil {

    /**
     * List结果结果分页
     * @param data
     * @return
     */
    public static PageInfo getPageInfo(List data,int PageNum,int PageSize) {
        List limitTask = new ArrayList<>();
        int limitStart = (PageNum - 1) * PageSize;
        int endStart = limitStart + PageSize;

        for(int index = limitStart ; index< endStart ; index++){
            if (index < data.size()) {
                limitTask.add(data.get(index));
            }
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotal(data.size());
        pageInfo.setPageNum(PageNum);
        pageInfo.setPageSize(PageSize);
        pageInfo.setList(limitTask);
        return pageInfo;
    }

    public static PageInfo getPageInfo(List data, int total, int pageNum, int pageSize){
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setList(data);
        return pageInfo;
    }

//    public static String data = "[\n" +
//            "                    {\n" +
//            "                        \"name\":\"纪涛\",\n" +
//            "                        \"mobile\":\"13784263958\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2024-01-07 18:11:20\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"华为消费者服务热线\",\n" +
//            "                        \"mobile\":\"950800\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":\"<EMAIL>\",\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-23 23:38:09\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"白宾的\",\n" +
//            "                        \"mobile\":\"15102651888\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-26 09:30:56\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"水泥砖老马\",\n" +
//            "                        \"mobile\":\"15128083407\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-10-24 19:14:42\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"苗国强\",\n" +
//            "                        \"mobile\":\"13932066803\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-26 09:30:56\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"威县工头\",\n" +
//            "                        \"mobile\":\"19931930569\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-09-10 09:36:45\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"馆陶广平卸水泥\",\n" +
//            "                        \"mobile\":\"15075091983\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-26 09:30:56\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"平的\",\n" +
//            "                        \"mobile\":\"13323007986\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-26 09:30:56\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"利飞\",\n" +
//            "                        \"mobile\":\"18532079067\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-26 09:30:56\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"李伟强\",\n" +
//            "                        \"mobile\":\"13370582130\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2021-01-06 09:04:43\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"路红兵\",\n" +
//            "                        \"mobile\":\"53182512257\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-26 09:30:56\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"路红兵\",\n" +
//            "                        \"mobile\":\"13864058818\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-26 09:30:56\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"建栋\",\n" +
//            "                        \"mobile\":\"13001712007\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-12-28 07:55:42\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"建栋\",\n" +
//            "                        \"mobile\":\"13854166665\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-12-21 09:03:02\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"郝利强\",\n" +
//            "                        \"mobile\":\"15075019945\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-26 09:30:56\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"许凤兰\",\n" +
//            "                        \"mobile\":\"13573113738\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-26 09:30:56\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"永年书军发货\",\n" +
//            "                        \"mobile\":\"5607878\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-26 09:30:56\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"许文乐\",\n" +
//            "                        \"mobile\":\"13026572219\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-07-22 12:39:49\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"平军\",\n" +
//            "                        \"mobile\":\"15354206671\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2021-04-18 12:01:45\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"佳鑫保安朱\",\n" +
//            "                        \"mobile\":\"18306410358\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-09-19 09:58:19\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"李熊\",\n" +
//            "                        \"mobile\":\"15733442773\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-08-01 07:50:46\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"临漳木板\",\n" +
//            "                        \"mobile\":\"15511089345\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-26 09:35:13\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"张浩杰\",\n" +
//            "                        \"mobile\":\"13969113259\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2021-01-05 09:40:54\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"周水生\",\n" +
//            "                        \"mobile\":\"53183168020\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-09-16 08:56:18\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"电瓶\",\n" +
//            "                        \"mobile\":\"13255317566\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-09-29 08:40:20\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"社清\",\n" +
//            "                        \"mobile\":\"18560198889\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-07-10 14:26:44\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"胡立普\",\n" +
//            "                        \"mobile\":\"13964052839\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-26 09:30:56\"\n" +
//            "                    },\n" +
//            "                    {\n" +
//            "                        \"name\":\"冯转校\",\n" +
//            "                        \"mobile\":\"15866731477\",\n" +
//            "                        \"company\":null,\n" +
//            "                        \"position\":null,\n" +
//            "                        \"email\":null,\n" +
//            "                        \"phoneSystem\":null,\n" +
//            "                        \"phoneType\":null,\n" +
//            "                        \"callCount\":null,\n" +
//            "                        \"calledTime\":null,\n" +
//            "                        \"remark\":null,\n" +
//            "                        \"createTime\":null,\n" +
//            "                        \"modifyTime\":\"2020-05-26 09:30:57\"\n" +
//            "                    }]";

//    public static void main(String[] args) {
//        List<AntiFraudDetail.UploadPhoneBookDetail> uploadPhoneBookDetails = JSONArray.parseArray(data, AntiFraudDetail.UploadPhoneBookDetail.class);
//        //System.out.println("uploadPhoneBookDetails = " + JSON.toJSONString(uploadPhoneBookDetails));
//        PageInfo pageInfo = getPageInfo(uploadPhoneBookDetails);
//        System.out.println("pageInfo = " + JSON.toJSONString(pageInfo));
//    }
}
