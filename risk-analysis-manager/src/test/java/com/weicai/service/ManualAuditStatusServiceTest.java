package com.weicai.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 人工审核状态服务测试类
 * 
 * <AUTHOR>
 * @since 2024/12/26
 */
@SpringBootTest
public class ManualAuditStatusServiceTest {
    
    @Resource
    private ManualAuditStatusService manualAuditStatusService;
    
    @Test
    public void testSingleUserAuditStatus() {
        String userKey = "test_user_001";
        
        // 测试初始状态
        boolean initialStatus = manualAuditStatusService.isInManualAudit(userKey);
        System.out.println("初始状态: " + initialStatus);
        
        // 设置进入审核状态
        manualAuditStatusService.enterAuditStatus(userKey, 300); // 5分钟过期
        boolean afterEnter = manualAuditStatusService.isInManualAudit(userKey);
        System.out.println("进入审核后状态: " + afterEnter);
        
        // 设置退出审核状态
        manualAuditStatusService.exitAuditStatus(userKey);
        boolean afterExit = manualAuditStatusService.isInManualAudit(userKey);
        System.out.println("退出审核后状态: " + afterExit);
    }
    
    @Test
    public void testBatchUserAuditStatus() {
        List<String> userKeys = Arrays.asList("test_user_002", "test_user_003", "test_user_004");
        
        // 测试批量进入审核状态
        manualAuditStatusService.batchEnterAuditStatus(userKeys, 300); // 5分钟过期
        
        // 检查每个用户的状态
        for (String userKey : userKeys) {
            boolean status = manualAuditStatusService.isInManualAudit(userKey);
            System.out.println("用户 " + userKey + " 审核状态: " + status);
        }
        
        // 测试批量退出审核状态
        manualAuditStatusService.batchExitAuditStatus(userKeys);
        
        // 再次检查每个用户的状态
        for (String userKey : userKeys) {
            boolean status = manualAuditStatusService.isInManualAudit(userKey);
            System.out.println("用户 " + userKey + " 退出后状态: " + status);
        }
    }
    
    @Test
    public void testHighConcurrency() {
        String userKey = "test_user_concurrent";
        
        // 模拟高并发查询
        for (int i = 0; i < 1000; i++) {
            boolean status = manualAuditStatusService.isInManualAudit(userKey);
            if (i % 100 == 0) {
                System.out.println("第 " + i + " 次查询，状态: " + status);
            }
        }
    }
}
