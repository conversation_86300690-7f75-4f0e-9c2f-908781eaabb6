# 人工审核状态查询接口实现方案

## 概述

本方案实现了一个高并发的人工审核状态查询接口，使用Redis作为缓存存储，通过自动化机制维护审核状态，支持高并发访问。

## 技术架构

### 1. 核心组件

- **Redis缓存**: 使用Redis存储审核状态，支持高并发读取
- **注解机制**: 通过AOP自动维护审核状态
- **批量处理**: 支持批量更新审核状态，提高性能
- **异常处理**: 完善的异常处理机制，确保系统稳定性

### 2. 关键类说明

#### 2.1 注解类
- `@AuditStatusCache`: 审核状态缓存注解，用于标记需要自动维护审核状态的方法

#### 2.2 切面类
- `AuditStatusCacheAspect`: AOP切面处理器，自动更新Redis缓存

#### 2.3 服务类
- `ManualAuditStatusService`: 审核状态查询服务接口
- `ManualAuditStatusServiceImpl`: 审核状态查询服务实现

#### 2.4 控制器类
- `ManualAuditStatusController`: 提供HTTP接口

#### 2.5 DTO/VO类
- `ManualAuditStatusQueryDTO`: 查询请求DTO
- `ManualAuditStatusVO`: 查询响应VO

## API接口

### 1. 查询审核状态（详细版）

**接口地址**: `POST /manualAuditStatus/query`

**请求参数**:
```json
{
    "userKey": "用户标识"
}
```

**响应结果**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "userKey": "用户标识",
        "isInManualAudit": true,
        "statusDesc": "正在人工审核中"
    }
}
```

### 2. 查询审核状态（简化版）

**接口地址**: `POST /manualAuditStatus/isInAudit`

**请求参数**:
```json
{
    "userKey": "用户标识"
}
```

**响应结果**:
```json
{
    "code": 200,
    "message": "success",
    "data": true
}
```

## Redis存储结构

### Key格式
```
manual_audit_status:{userKey}
```

### Value说明
- 存在且值为"1": 用户正在人工审核中
- 不存在: 用户未在人工审核中

### 过期时间
- 默认7天自动过期
- 可通过注解参数自定义过期时间

## 自动维护机制

### 1. 进入审核状态
当调用以下方法时，系统会自动将相关用户设置为审核中状态：
- `FraudCaseMainService.applyCase()` - 申请案件
- `FraudCaseMainService.allocationCase()` - 分配案件

### 2. 退出审核状态
当调用以下方法且为最终结论时，系统会自动将用户移出审核状态：
- `FraudCaseMainService.submitConclusion()` - 提交最终结论

## 性能特点

### 1. 高并发支持
- 使用Redis内存存储，查询性能极高
- 支持万级并发查询
- 响应时间通常在1-5ms

### 2. 批量处理
- 支持批量设置审核状态
- 减少Redis网络交互次数
- 提高整体性能

### 3. 容错机制
- 查询异常时返回false，不影响业务流程
- 完善的日志记录，便于问题排查
- 自动过期机制，避免数据堆积

## 使用示例

### 1. Java代码调用
```java
@Resource
private ManualAuditStatusService manualAuditStatusService;

// 查询单个用户状态
boolean isInAudit = manualAuditStatusService.isInManualAudit("userKey123");

// 手动设置用户进入审核状态
manualAuditStatusService.enterAuditStatus("userKey123", 7 * 24 * 60 * 60);

// 手动设置用户退出审核状态
manualAuditStatusService.exitAuditStatus("userKey123");

// 批量设置用户进入审核状态
List<String> userKeys = Arrays.asList("user1", "user2", "user3");
manualAuditStatusService.batchEnterAuditStatus(userKeys, 7 * 24 * 60 * 60);
```

### 2. HTTP接口调用
```bash
# 查询审核状态
curl -X POST http://localhost:8080/manualAuditStatus/query \
  -H "Content-Type: application/json" \
  -d '{"userKey":"test_user_001"}'

# 简化查询
curl -X POST http://localhost:8080/manualAuditStatus/isInAudit \
  -H "Content-Type: application/json" \
  -d '{"userKey":"test_user_001"}'
```

## 监控和运维

### 1. 日志监控
- 查询操作有DEBUG级别日志
- 状态变更有INFO级别日志
- 异常情况有ERROR级别日志

### 2. Redis监控
- 监控Redis连接状态
- 监控缓存命中率
- 监控内存使用情况

### 3. 性能监控
- 监控接口响应时间
- 监控并发访问量
- 监控错误率

## 注意事项

1. **数据一致性**: Redis缓存与数据库状态可能存在短暂不一致，但会自动同步
2. **过期时间**: 建议根据业务需求调整过期时间，避免过长或过短
3. **异常处理**: 查询异常时返回false，确保不影响主业务流程
4. **批量操作**: 大批量操作时注意分批处理，避免单次操作过大

## 扩展性

1. **支持集群**: Redis支持集群部署，可水平扩展
2. **支持分片**: 可根据userKey进行分片存储
3. **支持多数据中心**: 可配置Redis主从复制，支持多数据中心部署
