spring:
  cloud:
    #nacos注册中心配置
    nacos:
      discovery:
        server-addr: mse-d1186fc2-nacos-ans.mse.aliyuncs.com:8848
        namespace: 36ce07af-24e5-4a14-a500-b03b2c4a9173
    #网关配置
    gateway:
      httpclient:
        pool:
          max-idle-time: 10000
      routes:
        - id: manager_route
          uri: lb://risk-variable-manager
          predicates:
            - Path=/manager/**
        - id: api_route
          uri: lb://risk-variable-api
          predicates:
            - Path=/api/**
            - name: ApiReadBodyPredicateFactory
        - id: api_batch_route
          uri: lb://risk-variable-api-batch
          predicates:
            - Path=/api/**
            - name: ApiReadBodyPredicateFactory
        - id: analysis_route
          uri: lb://risk-analysis-manager
          predicates:
            - Path=/analysis/**
        - id: kudu_route
          uri: lb://risk-kudu-api
          predicates:
            - Path=/kudu/**
        - id: risk_route
          uri: http://antifraud-risk-admin.test.weicai.com.cn
          predicates:
            - Path=/risk/**
          filters:
            - StripPrefix=1 #截取path里面第一个，即/risk
            - PrefixPath=/api #在path头部添加/api
            - AddRequestHeader=variableToken, 111111111111111111111111111111111111
        - id: cp_route
          uri: http://cp-web.test.weicai.com.cn
          predicates:
            - Path=/cpweb/**
          filters:
            - StripPrefix=1 #截取path里面第一个，即/cpweb
            - PrefixPath=/api #在path头部添加/api
            - AddRequestHeader=variableToken, 111111111111111111111111111111111111
        - id: visual_route
          uri: http://risk-visual.test.weicai.com.cn
          predicates:
            - Path=/visual/**
          filters:
            - StripPrefix=1 #截取path里面第一个
            - AddRequestParameter=business, risk
            - AddRequestParameter=businessLine, risk
        - id: quota_manager_route
          uri: lb://risk-quota-manager
          predicates:
            - Path=/quota/manager/**
        - id: quota_api_route
          uri: lb://risk-quota-api
          predicates:
            - Path=/quota/api/**
      discovery:
        locator:
          enabled: true # 默认false, 如果不开启那么订阅程序将收不到nacos推送的消息
    loadbalancer:
      cache:
        ttl: 5s

    #限流配置
    sentinel:
      transport:
        # 不要配置clientId!!
        dashboard: infra-sentinel-dashboard.test.weicai.com.cn #控制台地址
      eager: true # 热加载
      filter:
        enabled: false
      datasource:
        #限流规则#https://aijishu.com/a/1060000000005326
        gw-flow-rules:
          nacos:
            server-addr: ${spring.cloud.nacos.discovery.server-addr}
            namespace: ${spring.cloud.nacos.discovery.namespace}
            data-id: sentinel-gateway-flow
            rule-type: gw-flow
            data-type: json
        gw-api-flow-rules:
          nacos:
            server-addr: ${spring.cloud.nacos.discovery.server-addr}
            namespace: ${spring.cloud.nacos.discovery.namespace}
            data-id: sentinel-gateway-api-group
            rule-type: gw-api-group
            data-type: json