package com.weicai.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;

import java.util.HashMap;
import java.util.Map;

@Configuration
@Data
@EnableKafka
@Slf4j
public class KafkaConfiguration {
    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    @Value("${spring.kafka.consumer.enable-auto-commit}")
    private boolean enableAutoCommit;
    @Value("${spring.kafka.consumer.group-id}")
    private String groupId;
    @Value("${spring.kafka.consumer.auto-offset-reset}")
    private String autoOffsetReset;
    @Value("${spring.kafka.consumer.auto-commit-interval}")
    private String autoCommitInterval;
    @Value("${spring.kafka.consumer.key-deserializer}")
    private String keyDeserializer;
    @Value("${spring.kafka.consumer.value-deserializer}")
    private String valueDeserializer;
    @Value("${spring.kafka.consumer.max-poll-records}")
    private String maxPollRecords;
    @Value("${spring.kafka.dp.group-id}")
    private String dpGroupId;
    /**
     * 衍生变量消费者组
     */
    @Value("${spring.kafka.var.group-id}")
    private String varGroupId;

    @Value("${spring.kafka.var.poll-count:1}")
    private Integer pollCount;
    @Value("${spring.kafka.var.fetch-min-bytes:65536}")
    private Integer fetchMinBytes;
    @Value("${spring.kafka.var.fetch-max-wait-ms:500}")
    private Integer fetchMaxWaitMs;
    @Value("${spring.kafka.var.concurrency:2}")
    private Integer concurrency;
    @Value("${spring.kafka.var.auto-commit-interval-ms:1000}")
    private Integer autoCommitIntervalMs;

    @Value("${spring.kafka.var.poll-time:30000}")
    private Integer pollTime;

    @Bean(name = "kafkaFactory")
    public DefaultKafkaProducerFactory<String, String> kafkaFactory() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, this.bootstrapServers);
        props.put(ProducerConfig.RETRIES_CONFIG, Integer.MAX_VALUE);
        props.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG, 3_000);
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 60_000);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 90 * 1024 * 1014);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, 10 * 1024 * 1024);
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "lz4");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 10 * 1024 * 1014);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 0);
        props.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 1);
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, false);
        return new DefaultKafkaProducerFactory<>(props);
    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate(
            @Qualifier("kafkaFactory") DefaultKafkaProducerFactory<String, String> factory) {
        return new KafkaTemplate<>(factory);
    }

    private Map<String, Object> consumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, enableAutoCommit);
        props.put(ConsumerConfig.GROUP_ID_CONFIG,groupId);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,autoOffsetReset);
        props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG,autoCommitInterval);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG,maxPollRecords);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        return props;
    }

    public ConsumerFactory<Integer, String> consumerFactory() {
        return new DefaultKafkaConsumerFactory<>(consumerConfigs());
    }

    @Bean(name = "kafkaContainerFactory")
    KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>> kafkaContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<Integer, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        factory.setConcurrency(3);
        factory.getContainerProperties().setPollTimeout(3000);
        return factory;
    }



    protected Map<String, Object> buildConsumerConfig(String group, String hosts) {
        Map<String, Object> propsMap = new HashMap<>(8);
        propsMap.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, hosts);
        propsMap.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);
        propsMap.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        propsMap.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        if (StringUtils.isNotBlank(group)) {
            propsMap.put(ConsumerConfig.GROUP_ID_CONFIG, group);
        }
        propsMap.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 8 * 1024 * 1024);
        propsMap.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 5_000);
        propsMap.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 90_000);
        propsMap.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 50);
        propsMap.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 60_000);
        return propsMap;
    }

    /**
     * dp主集群消息监听
     *
     * <AUTHOR>
     * @since 2023/8/1 15:08
     */
    @Bean(name = "dpKafkaListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> dpKafkaListenerContainerFactory(){
        Map<String, Object> config = buildConsumerConfig(dpGroupId, bootstrapServers);
        config.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        config.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 1);
        config.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 30_000);
        config.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 10_000);
        config.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 32_000);
        config.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        return buildContainerFactory(config,2);
    }

    /**
     * 衍生变量消费者组: 主集群
     * @return
     */
    @Bean(name = "varKafkaListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> varKafkaListenerContainerFactory(){
        Map<String, Object> config = buildConsumerConfig(varGroupId, bootstrapServers);
        config.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        log.info("varKafkaListenerContainerFactory pollCount = {} pollTime = {}",pollCount,pollTime);
        config.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, pollCount);
        config.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, pollTime);
        config.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 10_000);
        config.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 32_000);
        config.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, fetchMinBytes);
        config.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, fetchMaxWaitMs);
        config.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, autoCommitIntervalMs);
        config.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        return buildContainerFactory(config, concurrency);
    }

    protected ConcurrentKafkaListenerContainerFactory<String, String> buildContainerFactory(Map<String, Object> consumerConfig, Integer concurrency) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(consumerConfig));
        factory.setConcurrency(concurrency);
        return factory;
    }
}
