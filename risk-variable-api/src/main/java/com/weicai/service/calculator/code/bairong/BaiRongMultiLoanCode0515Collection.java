package com.weicai.service.calculator.code.bairong;
import com.google.common.collect.Lists;
import com.weicai.annotation.CodeService;
import com.weicai.dto.DataSourceResultDTO;
import com.weicai.entity.VariableInfo;
import com.weicai.service.calculator.code.AbstractCodeCal;
import org.springframework.stereotype.Component;

/**
 * 百融离线转实时133个
 *
 * <AUTHOR>
 * @since 2025/3/03 10:27
 */
public class BaiRongMultiLoanCode0515Collection extends AbstractBaiRongMultiLoanVarCodeCollection{
    @Component
    @CodeService(code = "fw_br_mult_loan_als_m1_m3_cell_bus_pdl_orgnum_ratio")
    public static class BrMultLoanAlsM1M3CellBusPdlOrgnumRatio extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            // 计算分子：als_m1_cell_pdl_orgnum
            double s1 = getSumVal(Lists.newArrayList("als_m1_cell_pdl_orgnum"), dataSource);
            // 计算分母：als_m1_cell_pdl_orgnum + 其他相关字段
            double s2 = getSumVal(Lists.newArrayList(
                    "als_m1_cell_pdl_orgnum",
                    "als_m1_cell_caon_orgnum",
                    "als_m1_cell_rel_orgnum",
                    "als_m1_cell_caoff_orgnum",
                    "als_m1_cell_cooff_orgnum",
                    "als_m1_cell_af_orgnum",
                    "als_m1_cell_coon_orgnum",
                    "als_m1_cell_oth_orgnum"
            ), dataSource);
            double d1 = (s2 == 0 ? 0 : s1 / s2);

            // 计算分子：als_m3_cell_pdl_orgnum
            double s3 = getSumVal(Lists.newArrayList("als_m3_cell_pdl_orgnum"), dataSource);
            // 计算分母：als_m3_cell_pdl_orgnum + 其他相关字段
            double s4 = getSumVal(Lists.newArrayList(
                    "als_m3_cell_pdl_orgnum",
                    "als_m3_cell_caon_orgnum",
                    "als_m3_cell_rel_orgnum",
                    "als_m3_cell_caoff_orgnum",
                    "als_m3_cell_cooff_orgnum",
                    "als_m3_cell_af_orgnum",
                    "als_m3_cell_coon_orgnum",
                    "als_m3_cell_oth_orgnum"
            ), dataSource);
            double d2 = (s4 == 0 ? 0 : s3 / s4);

            return d2 == 0 ? 0 : getDivideVal(d1, d2);
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_als_m3_m12_id_bus_af_allnum_ratio")
    public static class BrMultLoanAlsM3M12IdBusAfAllnumRatio extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            // 计算分子：als_m3_id_af_allnum
            double s1 = getSumVal(Lists.newArrayList("als_m3_id_af_allnum"), dataSource);
            // 计算分母：als_m3_id_pdl_allnum + 其他相关字段
            double s2 = getSumVal(Lists.newArrayList(
                    "als_m3_id_pdl_allnum",
                    "als_m3_id_caon_allnum",
                    "als_m3_id_rel_allnum",
                    "als_m3_id_caoff_allnum",
                    "als_m3_id_cooff_allnum",
                    "als_m3_id_af_allnum",
                    "als_m3_id_coon_allnum",
                    "als_m3_id_oth_allnum"
            ), dataSource);
            double d1 = (s2 == 0 ? 0 : s1 / s2);

            // 计算分子：als_m12_id_af_allnum
            double s3 = getSumVal(Lists.newArrayList("als_m12_id_af_allnum"), dataSource);
            // 计算分母：als_m12_id_pdl_allnum + 其他相关字段
            double s4 = getSumVal(Lists.newArrayList(
                    "als_m12_id_pdl_allnum",
                    "als_m12_id_caon_allnum",
                    "als_m12_id_rel_allnum",
                    "als_m12_id_caoff_allnum",
                    "als_m12_id_cooff_allnum",
                    "als_m12_id_af_allnum",
                    "als_m12_id_coon_allnum",
                    "als_m12_id_oth_allnum"
            ), dataSource);
            double d2 = (s4 == 0 ? 0 : s3 / s4);

            return d2 == 0 ? 0 : getDivideVal(d1, d2);
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_als_m3_m6_cell_org_bank_allnum_ratio")
    public static class BrMultLoanAlsM3M6CellOrgBankAllnumRatio extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            // 计算分子：als_m3_cell_bank_tra_allnum + als_m3_cell_bank_ret_allnum
            double s1 = getSumVal(Lists.newArrayList("als_m3_cell_bank_tra_allnum", "als_m3_cell_bank_ret_allnum"), dataSource);
            // 计算分母：als_m3_cell_bank_tra_allnum + als_m3_cell_bank_ret_allnum + 其他相关字段
            double s2 = getSumVal(Lists.newArrayList(
                    "als_m3_cell_bank_tra_allnum",
                    "als_m3_cell_bank_ret_allnum",
                    "als_m3_cell_nbank_nsloan_allnum",
                    "als_m3_cell_nbank_autofin_allnum",
                    "als_m3_cell_nbank_sloan_allnum",
                    "als_m3_cell_nbank_cons_allnum",
                    "als_m3_cell_nbank_finlea_allnum",
                    "als_m3_cell_nbank_else_allnum"
            ), dataSource);
            double d1 = (s2 == 0 ? 0 : s1 / s2);

            // 计算分子：als_m6_cell_bank_tra_allnum + als_m6_cell_bank_ret_allnum
            double s3 = getSumVal(Lists.newArrayList("als_m6_cell_bank_tra_allnum", "als_m6_cell_bank_ret_allnum"), dataSource);
            // 计算分母：als_m6_cell_bank_tra_allnum + als_m6_cell_bank_ret_allnum + 其他相关字段
            double s4 = getSumVal(Lists.newArrayList(
                    "als_m6_cell_bank_tra_allnum",
                    "als_m6_cell_bank_ret_allnum",
                    "als_m6_cell_nbank_nsloan_allnum",
                    "als_m6_cell_nbank_autofin_allnum",
                    "als_m6_cell_nbank_sloan_allnum",
                    "als_m6_cell_nbank_cons_allnum",
                    "als_m6_cell_nbank_finlea_allnum",
                    "als_m6_cell_nbank_else_allnum"
            ), dataSource);
            double d2 = (s4 == 0 ? 0 : s3 / s4);

            return d2 == 0 ? 0 : getDivideVal(d1, d2);
        }
    }


    @Component
    @CodeService(code = "fw_br_mult_loan_als_m3_m6_id_org_else_orgnum_ratio")
    public static class BrMultLoanAlsM3M6IdOrgElseOrgnumRatio extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            // 计算分子：als_m3_id_nbank_else_orgnum
            double s1 = getSumVal(Lists.newArrayList("als_m3_id_nbank_else_orgnum"), dataSource);
            // 计算分母：als_m3_id_bank_tra_orgnum + als_m3_id_bank_ret_orgnum + 其他相关字段
            double s2 = getSumVal(Lists.newArrayList(
                    "als_m3_id_bank_tra_orgnum",
                    "als_m3_id_bank_ret_orgnum",
                    "als_m3_id_nbank_nsloan_orgnum",
                    "als_m3_id_nbank_autofin_orgnum",
                    "als_m3_id_nbank_sloan_orgnum",
                    "als_m3_id_nbank_cons_orgnum",
                    "als_m3_id_nbank_finlea_orgnum",
                    "als_m3_id_nbank_else_orgnum"
            ), dataSource);
            double d1 = (s2 == 0 ? 0 : s1 / s2);

            // 计算分子：als_m6_id_nbank_else_orgnum
            double s3 = getSumVal(Lists.newArrayList("als_m6_id_nbank_else_orgnum"), dataSource);
            // 计算分母：als_m6_id_bank_tra_orgnum + als_m6_id_bank_ret_orgnum + 其他相关字段
            double s4 = getSumVal(Lists.newArrayList(
                    "als_m6_id_bank_tra_orgnum",
                    "als_m6_id_bank_ret_orgnum",
                    "als_m6_id_nbank_nsloan_orgnum",
                    "als_m6_id_nbank_autofin_orgnum",
                    "als_m6_id_nbank_sloan_orgnum",
                    "als_m6_id_nbank_cons_orgnum",
                    "als_m6_id_nbank_finlea_orgnum",
                    "als_m6_id_nbank_else_orgnum"
            ), dataSource);
            double d2 = (s4 == 0 ? 0 : s3 / s4);

            return d2 == 0 ? 0 : getDivideVal(d1, d2);
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_als_m6_cell_bus_rel_orgnum_rate")
    public static class BrMultLoanAlsM6CellBusRelOrgnumRate extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            // 计算分子：als_m6_cell_rel_orgnum
            double s1 = getSumVal(Lists.newArrayList("als_m6_cell_rel_orgnum"), dataSource);
            // 计算分母：als_m6_cell_pdl_orgnum + als_m6_cell_caon_orgnum + ... + als_m6_cell_oth_orgnum
            double s2 = getSumVal(Lists.newArrayList(
                    "als_m6_cell_pdl_orgnum",
                    "als_m6_cell_caon_orgnum",
                    "als_m6_cell_rel_orgnum",
                    "als_m6_cell_caoff_orgnum",
                    "als_m6_cell_cooff_orgnum",
                    "als_m6_cell_af_orgnum",
                    "als_m6_cell_coon_orgnum",
                    "als_m6_cell_oth_orgnum"
            ), dataSource);
            return s2 == 0 ? 0 : getDivideVal(s1, s2);
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_als_m6_id_bus_rel_allnum_rate")
    public static class BrMultLoanAlsM6IdBusRelAllnumRate extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            // 计算分子：als_m6_id_rel_allnum
            double s1 = getSumVal(Lists.newArrayList("als_m6_id_rel_allnum"), dataSource);
            // 计算分母：als_m6_id_pdl_allnum + als_m6_id_caon_allnum + ... + als_m6_id_oth_allnum
            double s2 = getSumVal(Lists.newArrayList(
                    "als_m6_id_pdl_allnum",
                    "als_m6_id_caon_allnum",
                    "als_m6_id_rel_allnum",
                    "als_m6_id_caoff_allnum",
                    "als_m6_id_cooff_allnum",
                    "als_m6_id_af_allnum",
                    "als_m6_id_coon_allnum",
                    "als_m6_id_oth_allnum"
            ), dataSource);
            return s2 == 0 ? 0 : getDivideVal(s1, s2);
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_cell_bank_ret_apply_orgcnt_3m_to_12m_trend")
    public static class BrMultLoanCellBankRetApplyOrgcnt3mTo12mTrendCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeDiviceValWithSub(dataSource, "als_m12_cell_bank_ret_orgnum", "als_m3_cell_bank_ret_orgnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_id_nbank_p2p_aplcnt_6m_to_12m_trend")
    public static class BrMultLoanIdNbankP2pAplcnt6mTo12mTrendCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeDiviceValWithSub(dataSource, "als_m12_id_nbank_p2p_allnum", "als_m6_id_nbank_p2p_allnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_id_pdl_apply_orgcnt_1m_to_3m_trend")
    public static class BrMultLoanIdPdlApplyOrgcnt1mTo3mTrendCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeDiviceValWithSub(dataSource, "als_m3_id_pdl_orgnum", "als_m1_id_pdl_orgnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_cell_coon_aplcnt_1m_to_6m_pct")
    public static class BrMultLoanCellCoonAplcnt1mTo6mPctCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeDivideVal(dataSource, "als_m1_cell_coon_allnum", "als_m6_cell_coon_allnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_cell_nbank_nsloan_aplcnt_15d_to_12m_pct")
    public static class BrMultLoanCellNbankNsloanAplcnt15dTo12mPctCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeDivideVal(dataSource, "als_d15_cell_nbank_nsloan_allnum", "als_m12_cell_nbank_nsloan_allnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_cell_pdl_apply_orgcnt_1m_to_3m_pct")
    public static class BrMultLoanCellPdlApplyOrgcnt1mTo3mPctCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeDivideVal(dataSource, "als_m1_cell_pdl_orgnum", "als_m3_cell_pdl_orgnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_id_bank_ret_aplcnt_3m_to_6m_pct")
    public static class BrMultLoanIdBankRetAplcnt3mTo6mPctCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeDivideVal(dataSource, "als_m3_id_bank_ret_allnum", "als_m6_id_bank_ret_allnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_id_nbank_else_aplcnt_15d_to_6m_pct")
    public static class BrMultLoanIdNbankElseAplcnt15dTo6mPctCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeDivideVal(dataSource, "als_d15_id_nbank_else_allnum", "als_m6_id_nbank_else_allnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_id_nbank_p2p_aplcnt_6m_to_12m_pct")
    public static class BrMultLoanIdNbankP2pAplcnt6mTo12mPctCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeDivideVal(dataSource, "als_m6_id_nbank_p2p_allnum", "als_m12_id_nbank_p2p_allnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_id_nbank_sloan_aplcnt_6m_to_12m_pct")
    public static class BrMultLoanIdNbankSloanAplcnt6mTo12mPctCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeDivideVal(dataSource, "als_m6_id_nbank_sloan_allnum", "als_m12_id_nbank_sloan_allnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_cell_nbank_aplcnt_6mbw3m")
    public static class BrMultLoanCellNbankAplcnt6mbw3mCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeSubVal(dataSource, "als_m6_cell_nbank_allnum", "als_m3_cell_nbank_allnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_cell_nbank_oth_aplcnt_3mbw1m")
    public static class BrMultLoanCellNbankOthAplcnt3mbw1mCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeSubVal(dataSource, "als_m3_cell_nbank_oth_allnum", "als_m1_cell_nbank_oth_allnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_cell_nbank_p2p_aplcnt_12mbw3m")
    public static class BrMultLoanCellNbankP2pAplcnt12mbw3mCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeSubVal(dataSource, "als_m12_cell_nbank_p2p_allnum", "als_m3_cell_nbank_p2p_allnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_id_bank_intedaymax_12mbw3m")
    public static class BrMultLoanIdBankIntedaymax12mbw3mCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeSubValWithNull(dataSource, "als_m12_id_bank_max_inteday", "als_m3_id_bank_max_inteday");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_id_nbank_p2p_aplcnt_12mbw1m")
    public static class BrMultLoanIdNbankP2pAplcnt12mbw1mCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeSubVal(dataSource, "als_m12_id_nbank_p2p_allnum", "als_m1_id_nbank_p2p_allnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_cell_nbank_ca_perorg_aplavg_6m")
    public static class BrMultLoanCellNbankCaPerorgAplavg6mCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeDivideVal(dataSource, "als_m6_cell_nbank_ca_allnum", "als_m6_cell_nbank_ca_orgnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_cell_nbank_night_perorg_aplavg_6m")
    public static class BrMultLoanCellNbankNightPerorgAplavg6mCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeDivideVal(dataSource, "als_m6_cell_nbank_night_allnum", "als_m6_cell_nbank_night_orgnum");
        }
    }

    @Component
    @CodeService(code = "fw_br_mult_loan_id_cooff_perorg_aplavg_3m")
    public static class BrMultLoanIdCooffPerorgAplavg3mCode extends AbstractCodeCal {
        @Override
        public Object cal(DataSourceResultDTO dataSource, VariableInfo variableInfo) {
            return getCodeDivideVal(dataSource, "als_m3_id_cooff_allnum", "als_m3_id_cooff_orgnum");
        }
    }
}
