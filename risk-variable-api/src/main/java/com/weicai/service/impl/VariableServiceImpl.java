package com.weicai.service.impl;

import cn.hutool.core.lang.TypeReference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.weicai.capturewait.service.VariableSubscribeService;
import com.weicai.common.RiskCallbackSystemEnum;
import com.weicai.constants.*;
import com.weicai.dao.VarDataRecordDAO;
import com.weicai.dao.VarKafkaRecordDAO;
import com.weicai.dao.VariableReqTaskDAO;
import com.weicai.dto.*;
import com.weicai.entity.*;
import com.weicai.enums.*;
import com.weicai.exception.BusinessException;
import com.weicai.exception.VariableCalculateException;
import com.weicai.exception.VariableException;
import com.weicai.nacos.NacosClientAdapter;
import com.weicai.query.DeriveTaskDTO;
import com.weicai.query.RiskBaseParam;
import com.weicai.query.VariableCalDTO;
import com.weicai.query.VariableDTO;
import com.weicai.redis.RedisService;
import com.weicai.remote.SyncHTTPRemoteAPI;
import com.weicai.service.*;
import com.weicai.service.cache.CacheService;
import com.weicai.service.relation.analyse.VariableRelationAnalyseHandler;
import com.weicai.util.ApplicationContextUtils;
import com.weicai.util.DateUtil;
import com.weicai.util.PointUtil;
import com.weicai.utils.WeChatUtils;
import com.weicai.vo.SubmitBankCardVo;
import com.weicai.vo.SubmitIdcardVo;
import com.weicai.vo.SubmitRegisterVo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.rapidoid.u.U;
import org.springframework.beans.BeanUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.weicai.dto.DataInputParam.GATEWAY_CREATE_TIME;

/**
 * <AUTHOR>
 * @date 2023年08月17日 下午1:59
 */
@Service
@Slf4j
public class VariableServiceImpl implements VariableService {
    @Resource
    private VariableHandlerFactory variableHandlerFactory;
    @Resource
    private CacheService cacheService;
    @Resource
    private VariableReqTaskDAO reqTaskDAO;
    @Resource
    private IdGenerateService idGenerateService;
    @Resource
    private VariableRelationAnalyseHandler variableRelationAnalyseHandler;
    @Resource
    private VariableDeriveRelationTaskService variableDeriveRelationTaskService;
    @Resource
    private RedisService redisService;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private VarKafkaRecordDAO kafkaRecordDAO;
    @Resource
    private VarDataRecordDAO varDataRecordDAO;
    @Resource
    private KafkaTemplate kafkaTemplate;
    @Resource
    private VariableReportService variableReportService;
    @Resource
    private DataCenterInsideService dataCenterInsideService;
    @Resource
    private PointUtil pointUtil;
    @Resource
    private DataSourceTemplateService dataSourceTemplateService;
    @Resource
    private VariableSubscribeService variableSubscribeService;

    private static String DERIVE_MODEL_MAP_KEY = "DERIVE_MODEL";

    @Override
    public JsonResult asyncCal(VariableDTO variableDTO) {
        // 前置校验
        checkParams(variableDTO);
        // 校验是否已经是在处理中了 如果已经在处理中 则直接返回成功
        if (cacheService.getRequestIdLock(variableDTO.getRequestId())) {
            log.info("asyncCal locked requestId:{}, loanKey:{}", variableDTO.getRequestId(), variableDTO.getLoanKey());
            return JsonResult.response(JsonResult.PROCESSING, "接收成功，处理中!");
        }
        // 设置异步请求标识
        variableDTO.setAsync(Boolean.TRUE);
        /** 补充数据源模版 ,用增等外部服务没有设置模板**/
        fillDataSourceTemplate(variableDTO);

        /** 补充参数,如查询dc设置四要素信息 **/
        acquireBaseParams(variableDTO);

        // 这一块 放异步处理 防止超时，引擎重试
        ThreadPoolConstants.VARIABLE_ANALYSE_THREAD_POOLS.execute(RunnableWrapper.of(() -> {
            /** 分组:依赖分析,记录流水,区分衍生和非衍生 **/
            MultiValueMap<String, Object> varAnalyse = varAnalyse(variableDTO);

            /** 补充数据源模版, 没有设置的 设置为默认模板，用增等外部服务没有设置模板，如果有衍生等变量会进行设置 **/
            fillDefaultDataSourceTemplate(variableDTO, varAnalyse);

            /** 开始异步计算, 分析后的没有衍生变量，衍生变量，不需要计算，会自动触发 **/
            parallelCalculate(variableDTO, varAnalyse);
        }));

        // 设置幂等，防止因为超时等原因导致的变量还在计算过程中导致计算错误
        cacheService.setRequestIdLock(variableDTO.getRequestId());
        return JsonResult.response(JsonResult.PROCESSING, "接收成功，处理中!");
    }

    private void checkParams(VariableDTO variableDTO) {
        if (CollectionUtils.isEmpty(variableDTO.getVarCodes())){
            throw new BusinessException("varCodes is empty error");
        }
        if (isEmptyDataSource(variableDTO)) {
            throw new BusinessException("数据源未配置");
        }
        // 判断变量是否包含空格
        for (String code : variableDTO.getVarCodes()){
            if (code.contains(" ")){
                throw new BusinessException(code + "varCode contains space error");
            }
        }
    }

    private void fillDataSourceTemplate(VariableDTO variableDTO) {
        /** dataInput结构里增加了GATEWAY_CREATE_TIME **/
        Map<String, Object> dataInput = variableDTO.getDataInput();
        Long gatewayCreateTime = dataInput.get(GATEWAY_CREATE_TIME) == null ? System.currentTimeMillis() : (Long) dataInput.get(GATEWAY_CREATE_TIME);
        Map<String, Long> dataSourceParamMap = variableDTO.getDataSourceParamMap();
        // 没有模版的会在后续依赖分析的时候赋为默认模板
        if (MapUtils.isEmpty(dataSourceParamMap)) {
            log.warn("dataSourceParamMap is empty, loanKey: {}, requestId: {}", variableDTO.getLoanKey(), variableDTO.getRequestId());
            return;
        }
        /**数据源和数据源模版的映射关系 **/
        dataSourceParamMap.forEach((source, templateId) -> {
            DataSourceTemplatePO dataSourceTemplate = cacheService.getDataSourceTemplate(source, templateId);
            /** 需要模版中配置了采集等待,需要计算一下 **/
            if (dataSourceTemplate.getUploadWindowTime()!=null && dataSourceTemplate.getUploadWaitTime() != null){
                dataSourceTemplate.setUploadTime(gatewayCreateTime - dataSourceTemplate.getUploadWindowTime() * 1000);
            }
            variableDTO.getDataSourceTemplateMap().putIfAbsent(source,dataSourceTemplate);
        });
    }

    private void fillDefaultDataSourceTemplate(VariableDTO variableDTO, MultiValueMap<String, Object> varAnalyse) {
        if (BusinessLineEnum.RIS.getCode().equals(variableDTO.getBusinessLine())) {
            return;
        }
        varAnalyse.keySet().forEach(source -> {
            if (!variableDTO.getDataSourceTemplateMap().containsKey(source)) {
                log.info("fillDefaultDataSourceTemplate dataSourceTemplateMap not contains loanKey:{}, key:{}",variableDTO.getLoanKey(), source);
                variableDTO.getDataSourceTemplateMap().putIfAbsent(source, DataSourceTemplatePO.defaultTemplate(source));
            }
        });
    }

    /**
     * @param variableDTO
     * @param readyCalculateVars : 准备计算的变量
     */
    private void parallelCalculate(VariableDTO variableDTO, MultiValueMap<String, Object> readyCalculateVars) {
        readyCalculateVars.forEach((k, v) ->{
            ThreadPoolConstants.VARIABLE_CAL_THREAD_POOLS.execute(RunnableWrapper.of(() -> {
                try {
                    /** 变量处理的参数 **/
                    VariableDTO dto = new VariableDTO();
                    BeanUtils.copyProperties(variableDTO, dto);
                    /** 每个组都有多个变量: 变量的list **/
                    List<String> varCodes = Lists.newArrayList((Set<String>) v.get(0));
                    dto.setVarCodes(varCodes);
                    /** 每个组对应的分组编号 **/
                    dto.setGroupId((String) v.get(1));
                    /** 同一批变量类型一致,并且是同源 **/
                    Integer type = cacheService.getVarInfoByCode(dto.getVarCodes().get(0)).getType();
                    dto.setVariableType(type);
                    //log.info("parallelCalculate {} -> {}", dto.getVarCodes().get(0), dto.getVariableType());
                    variableHandlerFactory.getVariableHandler(dto.getVariableType()).run(dto);
                } catch (Exception ex) {
                    log.error("parallelCalculate error variableDTO: {}, ex: ", JSON.toJSONString(variableDTO), ex);
                    throw ex;
                }
            }));
        });
    }

    private boolean isEmptyDataSource(VariableDTO variableDTO) {
        /** 数据源和数据源模版ID **/
        if (variableDTO.getDataSourceParamMap() == null) {
            log.error("asyncCal getDataSourceParamMap is null, requestId:{}, loanKey:{} ",variableDTO.getRequestId(), variableDTO.getLoanKey());
            // 需要报警
           // WeChatUtils.sendMessage("requestId:"+ variableDTO.getRequestId()+"数据源未配置");
            return true;
        }
        return false;
    }

    /**
     * 设置四要素等信息: 并行处理
     * @param dto
     */
    @SneakyThrows
    private void acquireBaseParams(VariableDTO dto) {
        // 兼容strategyId
        String strategyId = dto.getStrategyId();
        if (StringUtils.isBlank(strategyId)) {
            dto.setStrategyId(Optional.ofNullable(dto.getBaseParams()).orElseGet(RiskBaseParam::new).getStrategyId());
        }

        // 如果是首登的事件，则不查四要素信息,跳过,改为apollo配置
        // 2024-10-29 将_mirror 和_modelVerify 默认加入到这个配置中
        List<String> list = NacosClientAdapter.getListConfig("acquireBaseEventList",String.class);
        List<String> eventCodes = Lists.newArrayList();
        for (String eventCode :list) {
            eventCodes.add(eventCode);
            eventCodes.add(eventCode + Constants.MIRROR_EVENT_END_STR);
            eventCodes.add(eventCode + Constants.MODEL_VERIFY_EVENT_END_STR);
        }
        if (!eventCodes.contains(dto.getEventCode()) && (dto.getIsMirror() == null || !dto.getIsMirror())) {
            return;
        }
        long start = System.currentTimeMillis();
        String userKey = dto.getUserKey();
        if(dto.getBaseParams() == null) {
            dto.setBaseParams(new RiskBaseParam());
        }
        String apiLoanSource = dto.getBaseParams().getApiLoanSource();
        SubmitIdcardVo idCardVo = CompletableFuture.supplyAsync(() -> dataCenterService.queryIdCardInfo(userKey, apiLoanSource)).get(10, TimeUnit.SECONDS);
        SubmitRegisterVo registerVo = CompletableFuture.supplyAsync(() -> dataCenterService.queryRegisterInfo(userKey, apiLoanSource)).get(10, TimeUnit.SECONDS);
        SubmitBankCardVo bankCardVo = CompletableFuture.supplyAsync(() -> dataCenterService.queryBankCardInfo(userKey, apiLoanSource)).get(10, TimeUnit.SECONDS);

        if (StringUtils.isBlank(dto.getBaseParams().getBankCardNo())) {
            dto.getBaseParams().setBankCardNo(bankCardVo.getBankCardNo());
        }
        if (StringUtils.isBlank(dto.getBaseParams().getIdCardNo())) {
            dto.getBaseParams().setIdCardNo(idCardVo.getIdcardNumber());
        }
        if (StringUtils.isBlank(dto.getBaseParams().getName())) {
            dto.getBaseParams().setName(idCardVo.getIdcardName());
        }
        if (StringUtils.isBlank(dto.getBaseParams().getMobile())) {
            dto.getBaseParams().setMobile(registerVo.getMobile());
        }
        log.info("acquireBaseParams_finish userKey = {} cost = {}",userKey,(System.currentTimeMillis() - start)/1000);
    }

    /**
     * 对变量进行分组
     * 分衍生变量按数据源分组、衍生变量按依赖的变量交集分组
     * @param variableDTO
     * @return MultiValueMap
     * key:dataSource,
     * value: 0->List<String> 分组后的变量， 1-> groupId 分组id</>
     */
    private MultiValueMap<String, Object> varAnalyse(VariableDTO variableDTO) {
        log.info("groupVar start, userKey：{}, loanKey: {}, requestId: {}",variableDTO.getUserKey(), variableDTO.getLoanKey(), variableDTO.getRequestId());
        Stopwatch stopwatch = Stopwatch.createStarted();

        List<List<String>> parts = Lists.partition(variableDTO.getVarCodes(), 100);
        /** 获取缓存中的变量信息 **/
        Map<String, VariableInfo> varInfoCacheMap = parts.stream().map(cacheService::getVariableCache)
                .flatMap(Collection::stream).collect(Collectors.toMap(VariableInfo::getCode,
                Function.identity(), (o1, o2) -> o1, ConcurrentHashMap::new));

        /** 根据变量数据源分组: 衍生变量和模型特殊的分组DERIVE **/
        Map<String, Set<String>> varSourceMap = variableDTO.getVarCodes().stream().map(varInfoCacheMap::get).peek(var -> {
            if (var.getType().equals(VariableTypeEnum.DERIVE.getCode()) || var.getType().equals(VariableTypeEnum.MODEL.getCode())) {
                var.setVariableSource(DERIVE_MODEL_MAP_KEY);
            }
        }).collect(Collectors.groupingBy(VariableInfo::getVariableSource, Collectors.mapping(VariableInfo::getCode, Collectors.toSet())));

        /** 获取loanKey维度变量缓存 **/
        Map<Object, Object> variableCache = redisService.hGetAll(variableDTO.getLoanKey());
        // 获取数据源及依赖该数据源的所有变量（包括基础变量模型衍生变量）
        Map<Object, Object> datasourceDependVarsCache = redisService.hGetAll(RedisKeyConstants.DATASOURCE_DEPEND_VARS_KEY + variableDTO.getLoanKey());
        /** 处理有数据源的变量**/
        MultiValueMap<String, Object> dataSourceVarMultiMap = new LinkedMultiValueMap<>();
        dealDataSourceVars(variableDTO, varInfoCacheMap, variableCache, varSourceMap, dataSourceVarMultiMap, datasourceDependVarsCache);

        // 服务和统计变量数据源及依赖该数据源的所有衍生模型变量
        Map<String, Set<String>> datasourceDeriveAndModelMap = new HashMap<>(16);
        /** 处理衍生变量,因为衍生变量是在 handler中的after中计算,所以不需要加在dataSourceVarMultiMap 中计算 **/
        Set<Set<String>> deriveModelDependList = dealDeriveAndModelVars(variableDTO, variableCache, varSourceMap, varInfoCacheMap, datasourceDeriveAndModelMap, datasourceDependVarsCache);
        if (deriveModelDependList != null) {
            /** 将依赖的所有变量按照数据源再进行分组 合并出每个数据源真正需要计算的变量 **/
            mergedDataSourceVars(deriveModelDependList, dataSourceVarMultiMap, varInfoCacheMap);
        }

        // merge基础变量数据源关系和衍生模型变量依赖数据源
        Map<String, Set<String>> datasourceDependVarsMap = mergeDatasourceDependVarsAll(dataSourceVarMultiMap, datasourceDeriveAndModelMap, varInfoCacheMap);
        // 写入redis
        saveDatasourceDependVarsAll(variableDTO.getLoanKey(), datasourceDependVarsCache, datasourceDependVarsMap);

        log.info("groupVar end, userKey：{}, loanKey: {}, requestId: {}, size:{}, cost:{}",variableDTO.getUserKey(),
                variableDTO.getLoanKey(), variableDTO.getRequestId(), dataSourceVarMultiMap.size(), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        return dataSourceVarMultiMap;
    }

    /**
     * 处理有数据源的变量
     *
     * @param variableDTO
     * @param sourceVarMap
     * @param dataSourceVarMultiMap
     */
    private void dealDataSourceVars(VariableDTO variableDTO, Map<String, VariableInfo> varInfoCacheMap, Map<Object, Object> variableCache,
                                    Map<String, Set<String>> sourceVarMap, MultiValueMap<String, Object> dataSourceVarMultiMap, Map<Object, Object> datasourceDependVarsCache) {
        log.info("dealDataSourceVars map loanKey: {}, userKey: {}, size:{}",variableDTO.getLoanKey(), variableDTO.getUserKey(), sourceVarMap.size());
        Set<String> delCodes = Sets.newHashSet();

        // 获取缓存
        // 判断变量是否需要删除缓存
        sourceVarMap.forEach((dataSourceCode, vars) -> {
            // 过滤变量
            if (DERIVE_MODEL_MAP_KEY.equals(dataSourceCode)) {
                return;
            }
            //将分组信息插入map
            dataSourceVarMultiMap.add(dataSourceCode, vars);
            VariableReqTask task = buildReqGroupTask(variableDTO, vars);
            reqTaskDAO.insert(task);
            dataSourceVarMultiMap.add(dataSourceCode, task.getGroupId());

            if (MapUtils.isEmpty(variableCache)) {
                return;
            }
            // 判断缓存是否有效并且设置需要删除的变量
            Integer type = varInfoCacheMap.get(vars.iterator().next()).getType();
            judgeCacheAndSetDelVars(variableDTO, variableCache, delCodes, vars, type, dataSourceCode, datasourceDependVarsCache);
        });

        // 删除基础变量的缓存
        if (CollectionUtils.isNotEmpty(delCodes)) {
            log.info("dealDataSourceVars delVarCodes loanKey: {},  requestId:{}, delVarCodes:{}",  variableDTO.getLoanKey(), variableDTO.getRequestId(), JSON.toJSONString(delCodes));
            deleteVarAndModelCache(variableDTO, delCodes);
        }
    }

    /**
     * 获取需要删除缓存的变量
     * <AUTHOR>
     * @date 2024/11/15 17:45
     */
    private void judgeCacheAndSetDelVars(VariableDTO variableDTO, Map<Object, Object> variableCache, Set<String> delCodes,
                                         Set<String> varCodes, Integer type, String dataSourceCode, Map<Object, Object> datasourceDependVarsCache) {
        // 过滤非服务变量和统计变量
        if (!VariableTypeEnum.SERVICE.getCode().equals(type) && !VariableTypeEnum.STATISTICS.getCode().equals(type)) {
            return;
        }

        // 如果没有配置模板的按默认模板来计算，用增等外部服务
        DataSourceTemplatePO templatePO = variableDTO.getDataSourceTemplateMap().get(dataSourceCode);
        if (templatePO == null) {
            log.error("dealDataSourceVars DataSourceParamMap get key is null set default, loanKey:{}, key:{} ", variableDTO.getLoanKey(),  dataSourceCode);
            variableDTO.getDataSourceTemplateMap().put(dataSourceCode, DataSourceTemplatePO.defaultTemplate(dataSourceCode));
            templatePO= DataSourceTemplatePO.defaultTemplate(dataSourceCode);
        }

        // 如果是统计变量，并且没有没有采集等待时间则不做处理
        if (VariableTypeEnum.STATISTICS.getCode().equals(type)
                && (templatePO.getUploadWindowTime() == null ||  templatePO.getUploadWaitTime() == null)) {
            return;
        }

        // 只有服务变量和统计变量才有这个时间
        // 获取缓存开始时间
        Long cacheStartTime = getCacheStartTime(type, templatePO, variableDTO);
        Long cacheInTime = Optional.ofNullable(variableCache.get(dataSourceCode + RedisKeyConstants.VAR_CACHE_TIME))
                .map(s->Long.parseLong(String.valueOf(s))).orElse(null);

        DataSourceTemplatePO finalTemplatePO = templatePO;
        // 新老逻辑双跑验证
        List<String> newCodes;
        if (judgeCacheValidNew(cacheInTime, cacheStartTime, finalTemplatePO, type)) {
            newCodes = new ArrayList<>();
        } else {
            Object datasourceDependVars = datasourceDependVarsCache.get(dataSourceCode);
            if (datasourceDependVars == null) {
                newCodes = new ArrayList<>();
            } else {
                List<String> cacheCodes = JSON.parseObject((String) datasourceDependVars , new TypeReference<List<String>>() {
                });
                newCodes = (List<String>) CollectionUtils.intersection(cacheCodes, variableCache.keySet());
            }
        }

        List<String> oldCodes = varCodes.stream()
                .filter(code -> variableCache.get(code) != null)
                .filter(code-> {
                    // 缓存校验
                    CacheJudgeEnum cacheEnum = judgeCacheValidOld(code, cacheInTime, cacheStartTime, finalTemplatePO, type);
                    return cacheEnum == CacheJudgeEnum.DEL;
                }).collect(Collectors.toList());
        if (!new HashSet<>(newCodes).containsAll(oldCodes)) {
            log.error("judgeCacheAndSetDelVars datasourceCode: {}, newCodes: {} not containsAll oldCodes: {}", dataSourceCode, newCodes, oldCodes);
        }
        log.info("judgeCacheAndSetDelVars datasourceCode: {}, newCodes size: {}, oldCodes size: {}", dataSourceCode, newCodes.size(), oldCodes.size());

        boolean newCache = variableCacheDivide(variableDTO.getLoanKey());
        List<String> codes = (newCache ? newCodes : oldCodes);
        log.info("new cache divide: {}, loanKey: {}", newCache, variableDTO.getLoanKey());

        if (CollectionUtils.isEmpty(codes)) {
            return;
        }

        delCodes.addAll(codes);
        // 如果是统计变量需要清除资源包的缓存，如果是重试导致的删除那么还需要清楚采集等待
        if (VariableTypeEnum.STATISTICS.getCode().equals(type)) {
            log.info("remove STATISTICS cache, loanKey:{},requestId:{}, dataSourceCode:{} ", variableDTO.getLoanKey(), variableDTO.getRequestId(), dataSourceCode);

            // 删除对应资源包数据源缓存
            cacheService.delDwApiData(variableDTO.getLoanKey(), dataSourceCode);
            cacheService.delDataSourceJobId(variableDTO.getLoanKey(), dataSourceCode);
            //TODO 后续让引擎重试的时候带一个标识过来，然后根据标识判断删除
            variableSubscribeService.resetSubscribe(variableDTO.getLoanKey(), variableDTO.getRequestId(), dataSourceCode);
        }
    }

    private boolean variableCacheDivide(String loanKey) {
        Integer divideRatio = NacosClientAdapter.getIntConfig("new.cache.divide.ratio", 0);
        return Math.abs((long) loanKey.hashCode()) % 100 < divideRatio;
    }

    /**
     * 处理衍生的变量
     * 只要基础变量配置了使用缓存，那么衍生变量模型变量依赖这个基础变量的时候都是使用缓存
     * @param variableDTO
     * @param map
     */
    private Set<Set<String>> dealDeriveAndModelVars(VariableDTO variableDTO, Map<Object, Object> variableCache, Map<String, Set<String>> map,
                                                    Map<String, VariableInfo> varInfoCacheMap, Map<String, Set<String>> datasourceDeriveAndModelMap,
                                                    Map<Object, Object> datasourceDependVarsCache) {
        Set<String> deriveModelVars = map.get(DERIVE_MODEL_MAP_KEY);
        // 衍生变量为DERIVE_MODEL_MAP_KEY
        if (CollectionUtils.isEmpty(deriveModelVars)) {
            return null;
        }

        log.info("dealDeriveModelVars loanKey: {}, userKey: {}, size:{}",variableDTO.getLoanKey(), variableDTO.getUserKey(), deriveModelVars.size());
        // 分析衍生模型变量及其依赖的所有变量
        Map<String, Set<String>> deriveModelVarDependMap = analyseDeriveAndModel(deriveModelVars, varInfoCacheMap);
        // 分析子衍生子模型变量及其依赖的所有变量
        Map<String, Set<String>> subDeriveModelVarDependMap = analyseSubDeriveAndModel(deriveModelVarDependMap, varInfoCacheMap);
        // 分析所有衍生模型变量及其依赖的服务和统计变量数据源
        datasourceDeriveAndModelMap.putAll(analyseDeriveAndModelDependDatasource(deriveModelVarDependMap, subDeriveModelVarDependMap, varInfoCacheMap));
        // 第二次 衍生变量分组
        Map<Set<String>, Set<String>> deriveModelMap = groupDeriveAndModel(deriveModelVarDependMap);
        // 判断一下依赖的基础变量, 缓存是否在有效期内，如果有变动，那么删除衍生变量缓存 和基础变量缓存
        deriveAndModelVarCacheProcess(variableDTO, variableCache, deriveModelMap, varInfoCacheMap, datasourceDependVarsCache);
        // 插入衍生变量任务
        // 20241119这一步放在缓存处理后面，这样在首次处理变量缓存的时候 就不会影响
        // 衍生变量 和模型变量都放一块去调用，原始的衍生变量,
        insertDeriveTask(variableDTO, deriveModelVars, varInfoCacheMap);
        // 插入流水表，注意不要将依赖分析后的变量插入流水表
        deriveModelMap.values().forEach(actualVars -> {
            // 实际插入的衍生变量组
            VariableReqTask task = buildReqGroupTask(variableDTO, actualVars);
            // 记录组id缓存
            Map<String, Object> groupMap = actualVars.stream().collect(HashMap::new, (m, s) -> m.put(s, task.getGroupId()), HashMap::putAll);
            cacheService.setDeriveVarGroupCache(variableDTO.getLoanKey(), groupMap);
            reqTaskDAO.insert(task);
        });

        return deriveModelMap.keySet();
    }

    /**
     * 合并有数据源的变量
     *
     * @param deriveModelDependList
     * @param dataSourceVarMultiMap
     */
    private void mergedDataSourceVars(Set<Set<String>> deriveModelDependList, MultiValueMap<String, Object> dataSourceVarMultiMap,
                                      Map<String, VariableInfo> varInfoCacheMap) {
        // 过滤其中依赖分析出的衍生变量
        Map<String, Set<String>> deriveModelVarDSMap = deriveModelDependList.stream()
                .flatMap(Collection::stream)
                .map(code -> {
                    VariableInfo info = varInfoCacheMap.get(code);
                    if (info != null) {
                        return info;
                    }

                    // 兜底
                    log.info("mergedDataSourceVars cache is no hit, key:{}", code);
                    return cacheService.getVarInfoByCode(code);
                })
                // 过滤衍生变量不需要合并
                .filter(var -> !VariableTypeEnum.DERIVE.getCode().equals(var.getType()))
                .filter(var -> !VariableTypeEnum.MODEL.getCode().equals(var.getType()))
                .collect(Collectors.groupingBy(VariableInfo::getVariableSource,
                        Collectors.mapping(VariableInfo::getCode, Collectors.toSet())));

        // 合并已有的数据源 并且合并
        for (Map.Entry<String, Set<String>> entry : deriveModelVarDSMap.entrySet()) {
            String key = entry.getKey();

            // 如果key在map2中也存在，进行合并操作
            if (dataSourceVarMultiMap.containsKey(key)) {
                ((Set<String>) (dataSourceVarMultiMap.get(key)).get(0)).addAll(entry.getValue());
                continue;
            }

            // 否者插入新的，并生成groupId,不用写表因为不会返回
            dataSourceVarMultiMap.add(key, entry.getValue());
            dataSourceVarMultiMap.add(key, idGenerateService.generateId(IdGenerateService.GROUP_ID_TASK).toString());
        }
    }

    private Map<String, Set<String>> mergeDatasourceDependVarsAll(MultiValueMap<String, Object> dataSourceVarMultiMap,
                                                                  Map<String, Set<String>> datasourceDeriveAndModelMap,
                                                                  Map<String, VariableInfo> varInfoCacheMap) {
        Map<String, Set<String>> datasourceBaseMap = dataSourceVarMultiMap.entrySet().stream().filter(entry -> {
            String var = new ArrayList<>((Set<String>) entry.getValue().get(0)).get(0);
            VariableInfo info = varInfoCacheMap.get(var);
            if (info == null) {
                info = cacheService.getVarInfoByCode(var);
                varInfoCacheMap.put(var, info);
            }
            return VariableTypeEnum.SERVICE.getCode().equals(info.getType()) || VariableTypeEnum.STATISTICS.getCode().equals(info.getType());
        }).collect(Collectors.toMap(Map.Entry::getKey, entry -> (Set<String>) entry.getValue().get(0)));

        datasourceDeriveAndModelMap.forEach((key, value) -> {
            datasourceBaseMap.computeIfPresent(key, (k, v) -> {
                value.addAll(v);
                return value;// 合并两个Set
            });
            datasourceDeriveAndModelMap.put(key, value);
        });

        datasourceBaseMap.forEach((key, value) -> {
            if (!datasourceDeriveAndModelMap.containsKey(key)) {
                datasourceDeriveAndModelMap.put(key, value);
            }
        });

        return datasourceDeriveAndModelMap;
    }

    private void saveDatasourceDependVarsAll(String loanKey, Map<Object, Object> datasourceDependVarsCache, Map<String, Set<String>> datasourceDependVarsMap) {
        Map<String, List<String>> cacheDatasourceDependMap = datasourceDependVarsCache.entrySet().stream()
                .collect(Collectors.toMap(entry -> (String) entry.getKey(),
                        entry -> JSON.parseObject((String) entry.getValue(), new TypeReference<List<String>>() {
                        })));

        cacheDatasourceDependMap.forEach((key, value) -> {
            Set<String> cacheValues = new HashSet<>(value);
            datasourceDependVarsMap.computeIfPresent(key, (k, v) -> {
                cacheValues.addAll(v);
                return cacheValues;// 合并两个Set
            });
            cacheDatasourceDependMap.put(key, new ArrayList<>(cacheValues));
        });

        datasourceDependVarsMap.forEach((key, value) -> {
            if (!cacheDatasourceDependMap.containsKey(key)) {
                cacheDatasourceDependMap.put(key, new ArrayList<>(value));
            }
        });

        Map<String, Object> finalCacheDatasourceDependMap = cacheDatasourceDependMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                entry -> JSONArray.toJSONString(entry.getValue())));
        long cacheTime = NacosClientAdapter.getLongConfig(NacosConfigConstants.DATASOURCE_DEPEND_VAR_CACHE_TIMEOUT_HOURS, 2L);
        redisService.hPutAll(RedisKeyConstants.DATASOURCE_DEPEND_VARS_KEY + loanKey, finalCacheDatasourceDependMap, cacheTime);
    }

    /**
     * 构建流水表
     *
     * @param variableDTO
     * @param vars
     * @return
     */
    private VariableReqTask buildReqGroupTask(VariableDTO variableDTO, Set<String> vars) {
        return new VariableReqTask()
                .setUserKey(variableDTO.getUserKey())
                .setLoanKey(variableDTO.getLoanKey())
                .setEventCode(variableDTO.getEventCode())
                .setRequestId(variableDTO.getRequestId())
                .setGroupId(idGenerateService.generateId(IdGenerateService.GROUP_ID_TASK).toString())
                .setSourceSystem(variableDTO.getSourceSystem())
                .setCallbackSystem(variableDTO.getCallbackSystem())
                .setStatus(VariableReqTaskStatusEnum.INIT.getCode())
                .setAllVar(Lists.newArrayList(vars));
    }

    /**
     * 构建流水表
     *
     * @param variableDTO
     * @param vars        衍生变量
     * @return
     */
    private void insertDeriveTask(VariableDTO variableDTO, Set<String> vars,  Map<String, VariableInfo> varInfoCacheMap) {
        String strategyId = variableDTO.getStrategyId();
        if (StringUtils.isBlank(strategyId)) {
            strategyId = Optional.ofNullable(variableDTO.getBaseParams()).orElseGet(RiskBaseParam::new).getStrategyId();
        }
        String processSplitFlowType  = "";
        if (variableDTO.getDataInput() != null) {
            processSplitFlowType = String.valueOf(variableDTO.getDataInput().get(Constants.SPLIT_FLOW_TYPE));
        }

        DeriveTaskDTO dto = DeriveTaskDTO.builder()
                .userKey(variableDTO.getUserKey())
                .loanKey(variableDTO.getLoanKey())
                .eventCode(variableDTO.getEventCode())
                .sourceSystem(variableDTO.getSourceSystem())
                .requestId(variableDTO.getRequestId())
                .strategyId(strategyId)
                .businessLine(variableDTO.getBusinessLine())
                .processSplitFlowType(processSplitFlowType)
                .allDeriveVar(Lists.newArrayList(vars)).build();

        //如果是仅使用缓存的变量,需要排除
        variableDeriveRelationTaskService.insertDeriveRelationTask(dto, varInfoCacheMap);
    }

    /**
     * 对衍生变量和模型变量进行分组，依赖的变量有交集的分为一组
     *
     * @param varMap
     * @return key: Set<String> 交集衍生变量依赖所有的变量集合, v: Set<String> 有交集的变量集合
     */
    private Map<Set<String>, Set<String>> groupDeriveAndModel(Map<String, Set<String>> varMap) {
        // 将变量进行分组，有交集的变量归为一组，并将依赖变量进行合并， 这其中key会包含衍生变量
        // 比如 原先是 1：a,b,c 2: b,d,e, 合并后， a,b,c,d,e: 1,2
        Map<Set<String>, Set<String>> groupedValues = new HashMap<>();
        for (Map.Entry<String, Set<String>> entry : varMap.entrySet()) {
            String key = entry.getKey();
            Set<String> value = entry.getValue();

            boolean hasIntersection = false;
            for (Map.Entry<Set<String>, Set<String>> m : groupedValues.entrySet()) {
                // 判断两个集合是否有交集
                if (!Collections.disjoint(m.getKey(), value)) {
                    hasIntersection = true;
                    m.getKey().addAll(value);
                    m.getValue().add(key);
                    break;
                }
            }

            if (!hasIntersection) {
                groupedValues.put(new HashSet<>(value), new HashSet<>(Collections.singleton(key)));
            }
        }

        return groupedValues;
    }

    private Map<String, Set<String>> analyseDeriveAndModel(Set<String> vars, Map<String, VariableInfo> varInfoCacheMap) {
        // 将依赖关系拍平， key:是衍生变量， v:衍生变量依赖的所有变量包含里衍生和基础变量
        Map<String, Set<String>> varMap = vars.stream()
                .map(varCode -> variableRelationAnalyseHandler.getAnalyserByType(RelationAnalyseEnum.PARENT).analyse(varCode))
                .collect(Collectors.toMap(VariableRelationTreeVO::getVariableCode,
                        vo -> vo.getDeriveVariableParentMap().values().stream()
                                .flatMap(Collection::stream)
                                .collect(Collectors.toSet())
                        , (existingSet, newSet) -> {
                            existingSet.addAll(newSet);
                            return existingSet;
                        }));

        // 从缓存加载依赖变量的配置信息，写入varInfoCacheMap
        Set<String> otherCodes = varMap.values().stream()
                .flatMap(Collection::stream)
                .filter(code -> varInfoCacheMap.get(code) == null)
                .collect(Collectors.toSet());
        List<List<String>> parts = Lists.partition(new ArrayList<>(otherCodes), 100);
        parts.stream()
                .map(cacheService::getVariableCache)
                .flatMap(Collection::stream)
                .forEach(varInfo -> varInfoCacheMap.put(varInfo.getCode(), varInfo));

        return varMap;
    }

    private Map<String, Set<String>> analyseSubDeriveAndModel(Map<String, Set<String>> deriveModelVarDependMap, Map<String, VariableInfo> varInfoCacheMap) {
        Set<String> subDeriveAndModelVars = deriveModelVarDependMap.values().stream().flatMap(Collection::stream).filter(var -> {
            VariableInfo info = varInfoCacheMap.get(var);
            if (info == null) {
                info = cacheService.getVarInfoByCode(var);
                varInfoCacheMap.put(var, info);
            }
            return VariableTypeEnum.MODEL.getCode().equals(info.getType()) || VariableTypeEnum.DERIVE.getCode().equals(info.getType());
        }).collect(Collectors.toSet());

        return analyseDeriveAndModel(subDeriveAndModelVars, varInfoCacheMap);
    }

    private Map<String, Set<String>> analyseDeriveAndModelDependDatasource(Map<String, Set<String>> deriveModelVarDependMap,
                                                                           Map<String, Set<String>> subDeriveModelVarDependMap, 
                                                                           Map<String, VariableInfo> varInfoCacheMap) {
        Map<String, Set<String>> datasourceDeriveAndModelMap = new HashMap<>(16);
        doAnalyseDeriveAndModelDependDatasource(deriveModelVarDependMap, varInfoCacheMap, datasourceDeriveAndModelMap);

        doAnalyseDeriveAndModelDependDatasource(subDeriveModelVarDependMap, varInfoCacheMap, datasourceDeriveAndModelMap);

        return datasourceDeriveAndModelMap;
    }

    private void doAnalyseDeriveAndModelDependDatasource(Map<String, Set<String>> deriveModelVarDependMap, Map<String, VariableInfo> varInfoCacheMap, Map<String, Set<String>> datasourceDeriveAndModelMap) {
        deriveModelVarDependMap.forEach((key, value) -> {
            Set<String> dependDatasourceCodes = value.stream().filter(var -> {
                VariableInfo info = varInfoCacheMap.get(var);
                if (info == null) {
                    info = cacheService.getVarInfoByCode(var);
                    varInfoCacheMap.put(var, info);
                }
                return VariableTypeEnum.SERVICE.getCode().equals(info.getType()) || VariableTypeEnum.STATISTICS.getCode().equals(info.getType());
            }).map(var -> varInfoCacheMap.get(var).getVariableSource()).collect(Collectors.toSet());

            dependDatasourceCodes.forEach(datasourceCode -> datasourceDeriveAndModelMap.computeIfAbsent(datasourceCode, v -> new HashSet<>()).add(key));
        });
    }

    /**
     * 衍生变量和模型变量依赖的变量缓存处理
     *
     * @param groupedValues 分组好的依赖变量，key是依赖的变量 其中包含基础变量和衍生变量，v是衍生变量
     */
    private void deriveAndModelVarCacheProcess(VariableDTO dto, Map<Object, Object> variableCache, Map<Set<String>, Set<String>> groupedValues,
                                               Map<String, VariableInfo> varInfoCacheMap, Map<Object, Object> datasourceDependVarsCache) {
        // 1. 获取缓存，如果没有缓存则不需要处理衍生和模型变量了
        if (MapUtils.isEmpty(variableCache)) {
            return;
        }

        log.info("deriveAndModelVarCacheProcess loanKey: {},  requestId:{}, vars:{}",  dto.getLoanKey(), dto.getRequestId(), JSON.toJSONString(groupedValues));
        groupedValues.forEach((baseVarCodes, deriveVars) -> {
            // 转换取出缓存中的对应的缓存时间
            Map<String, List<JSONObject>> baseVarInfosMap = baseVarCodes.stream().map(code -> {
                VariableInfo info = varInfoCacheMap.get(code);
                if (info == null) {
                    info = cacheService.getVarInfoByCode(code);
                    varInfoCacheMap.put(code, info);
                }

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("code", info.getCode());
                jsonObject.put("source", info.getVariableSource());
                jsonObject.put("type", info.getType());

                return jsonObject;
            }).collect(Collectors.groupingBy(jsonObject -> jsonObject.getString("source")));

            // 2. 判断缓存时间和数据源时间
            Set<String> delVarCodes = Sets.newHashSet();
            Set<String> delDeriveVarCodes = Sets.newHashSet();

            for (Map.Entry<String, List<JSONObject>> entry: baseVarInfosMap.entrySet()) {
                String source = entry.getKey();
                Integer type = entry.getValue().get(0).getInteger("type");
                Set<String> varCodes =entry.getValue().stream().map(jsonObject->jsonObject.getString("code")).collect(Collectors.toSet());
                //衍生变量或者模型变量直接跳过, 但是需要加到删除缓存的set中，后续如果涉及到需要删除的再具体判断
                if (VariableTypeEnum.DERIVE.getCode().equals(type) || VariableTypeEnum.MODEL.getCode().equals(type)) {
                    delDeriveVarCodes.addAll(varCodes);
                    continue;
                }

                // 判断并设置需要删除的变量
                judgeCacheAndSetDelVars(dto, variableCache, delVarCodes, varCodes, type, source, datasourceDependVarsCache);
            }

            if (CollectionUtils.isEmpty(delVarCodes)) {
                return;
            }
            // 删除基础变量和衍生变量的缓存
            if (CollectionUtils.isNotEmpty(delDeriveVarCodes)) {
                // 过滤derive中是否使用缓存，并且包含在delVarCodes
                Set<String> codes = delDeriveVarCodes.stream()
                        .map(varCode -> variableRelationAnalyseHandler.getAnalyserByType(RelationAnalyseEnum.PARENT).analyse(varCode))
                        .filter(treeVo -> treeVo.getDeriveVariableParentMap().values()
                                .stream()
                                .flatMap(Collection::stream)
                                .anyMatch(delVarCodes::contains))
                        .map(VariableRelationTreeVO::getVariableCode)
                        .collect(Collectors.toSet());
                // 加入到删除的集合中
                delVarCodes.addAll(codes);
            }

            delVarCodes.addAll(deriveVars);
            log.info("deriveAndModelVarCacheProcess delVarCodes loanKey: {},  requestId:{}, delVarCodes:{}",  dto.getLoanKey(), dto.getRequestId(), JSON.toJSONString(delVarCodes));
            deleteVarAndModelCache(dto, delVarCodes);
        });

    }

    /**
     * 删除变量缓存 以及 衍生和模型redis bitmap中的占位符
     * 因为重试的时候 缓存删掉了，但是占位符没有清除
     * <AUTHOR>
     * @date 2024/11/19 12:02
     * @param dto
     * @param delVarCodes
     */
    private void deleteVarAndModelCache(VariableDTO dto, Set<String> delVarCodes) {
        redisService.hDelete(dto.getLoanKey(), delVarCodes.toArray());
        variableDeriveRelationTaskService.delVarsFromRedisTask(dto.getRequestId(), delVarCodes);
    }

    /**
     * 判断当前缓存是否可以使用
     * <AUTHOR>
     * @date 2024/10/31 14:29
     * @return Boolean
     */
    private Boolean judgeCacheValidNew(Long cacheInTime, Long cacheStartTime, DataSourceTemplatePO templatePO, Integer type) {
        // 如果服务变量没有这个时间，是数据源未命中缓存，未查得的情况
        // 如果是统计变量则是未采到数据
        if (cacheInTime == null) {
            return Boolean.FALSE;
        }

        Integer onlyUseCache = templatePO.getOnlyUseCache();
        if (VariableTypeEnum.SERVICE.getCode().equals(type) && onlyUseCache != null) {
            if (onlyUseCache == UseCacheEnum.NO_USE.getValue()){
                return Boolean.FALSE;
            }
        }

        if (cacheInTime < cacheStartTime) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    private CacheJudgeEnum judgeCacheValidOld(String code, Long cacheInTime, Long cacheStartTime,
                                           DataSourceTemplatePO templatePO, Integer type) {
        // 如果服务变量没有这个时间，是数据源未命中缓存，未查得的情况
        // 如果是统计变量则是未采到数据
        if (cacheInTime == null) {
            return CacheJudgeEnum.DEL;
        }

        Integer onlyUseCache = templatePO.getOnlyUseCache();
        if (VariableTypeEnum.SERVICE.getCode().equals(type) && onlyUseCache != null) {
            if (onlyUseCache == UseCacheEnum.NO_USE.getValue()){
                return CacheJudgeEnum.DEL;
            }
        }

        if (cacheInTime < cacheStartTime) {
            return CacheJudgeEnum.DEL;
        }

        return CacheJudgeEnum.RESERVE;
    }


    /**
     * 获取当前时间减去模版中的时间
     * <AUTHOR>
     * @date 2024/10/31 15:52
     * @return long
     */
    private Long getCacheStartTime(Integer type,DataSourceTemplatePO templatePO, VariableDTO dto) {
        if (VariableTypeEnum.STATISTICS.getCode().equals(type)) {
            // 这个时间已经是减完后的时间
            return templatePO.getUploadTime();
        }

        // 20241118变量中心变量缓存的缓存时间计算逻辑和DP计算数据源缓存的逻辑保持一致，天维度；
        // 服务变量2024-10-31 增加第二缓存表达式判断
        Integer cacheDay = Optional.ofNullable(templatePO.getCacheDay()).orElse(0);
        String cacheTime = dataSourceTemplateService.secondCacheExpressionExecute(templatePO.getSecondCacheExpression(), cacheDay.toString(), dto);
        // 如果在缓存时间内,和dp的getDistDates 等效，直接用当前日期，最后加1毫秒 等于是不包含临界值
        return DateUtil.addDaysToDate(new Date(), - Integer.parseInt(cacheTime) -1).getTime() + 1;
    }

    /**
     * 更新流水表并判断是否回调引擎
     * 1、获取groupID
     * 2、过滤出真正需要回调的变量
     * 3、计算条件判断
     * @param dto
     */
    @Override
    public void updateReqTask(HandlerResultDTO dto, VariableCalDTO calDTO) {
        boolean logFlag = NacosClientAdapter.getBooleanConfig(NacosConfigConstants.LOG_DETAIL, false);

        // 特殊处理
        specialProcess(dto, calDTO);

        // 排除一期接口变量
        if (StringUtils.isNotBlank(calDTO.getVarGroupCode()) || StringUtils.isBlank(calDTO.getRequestId())) {
            if (logFlag) {
                log.info("updateReqTask requestId is blank, requestId:{}, loanKey:{}, userKey:{}", calDTO.getRequestId(), calDTO.getLoanKey(), calDTO.getUserKey());
            }
            return;
        }

        List<String> groupIds = getGroupIds(calDTO, null);
        if (logFlag) {
            log.info("updateReqTask start , requestId: {}, loanKey: {}, userKey: {} ,dto={} , groupIds: {}",
                    calDTO.getRequestId(), calDTO.getLoanKey(),calDTO.getUserKey(), JSON.toJSONString(dto), groupIds);
        }
        /** 如果是不需要回调的变量刚好分为一组了 **/
        if (CollectionUtils.isEmpty(groupIds)) {
            if (logFlag) {
                log.info("updateReqTask groupId is blank, requestId:{}, loanKey:{}, userKey:{}", calDTO.getRequestId(), calDTO.getLoanKey(), calDTO.getUserKey());
            }
            return;
        }

        for (String groupId : groupIds) {
            updateReqTaskByGroupId(dto, calDTO, groupId, logFlag);
        }
    }

    private void updateReqTaskByGroupId(HandlerResultDTO dto, VariableCalDTO calDTO, String groupId, boolean logFlag) {
        /** 查询流水表：用于后续计算使用，流水表没有的 说明不需要回调 **/
        VariableReqTask task = reqTaskDAO.selectByReqIdAndGroupId(calDTO.getRequestId(), groupId);
        if (task == null) {
            if (logFlag) {
                log.info("updateReqTask task is blank, requestId:{}, loanKey:{}, userKey:{}", calDTO.getRequestId(), calDTO.getLoanKey(), calDTO.getUserKey());
            }
            return;
        }

        // ps:在上线的时候 多台机器在同时消费一条消息的情况下还是会出现，多次回调，直接返回
        if (VariableReqTaskStatusEnum.FAIL.getCode().equals(task.getStatus())
                || VariableReqTaskStatusEnum.SUCCESS.getCode().equals(task.getStatus())
                || VariableReqTaskStatusEnum.PART_SUCCESS.getCode().equals(task.getStatus())) {
            log.warn("updateReqTask task is callbacked, requestId:{}, loanKey:{}, userKey:{}, vars:{}",
                    calDTO.getRequestId(), calDTO.getLoanKey(), calDTO.getUserKey(), JSON.toJSONString(calDTO.getOriginVarCodes()));
            return;
        }

        /** 过滤出真正需要回调的变量: 因为计算的出来的变量和真实时候的变量可能不一样 **/
        Set<String> curFinishVarCodes = dto.getVarResults().stream().map(VariableResult::getCode).filter(task.getAllVar()::contains).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(curFinishVarCodes)) {
            if (logFlag) {
                log.info("updateReqTask varCodes is empty, requestId:{}, loanKey:{}, userKey:{}, vars:{}", calDTO.getRequestId(),
                        calDTO.getLoanKey(), calDTO.getUserKey(), StringUtils.abbreviate(JSON.toJSONString(calDTO.getVarCodes()), Constants.MAX_WIDTH));
            }
            return;
        }
        /** curFinishVarCodes是这次需要完成的 **/
        UpdateVariableReqTaskDTO updateDto = UpdateVariableReqTaskDTO.builder()
                .requestId(calDTO.getRequestId()).groupId(groupId)
                .status(VariableReqTaskStatusEnum.PROCESSING.getCode())
                .finishVars(new ArrayList<>(curFinishVarCodes)).build();

        /** 更新流水表 **/
        VariableReqTask reqTask = reqTaskDAO.updateByReqIdAndGroupId(updateDto);
        if (logFlag) {
            log.info("updateReqTask reqTask, requestId: {}, loanKey: {}, userKey: {} ,dto={} , groupId: {}, curFinishVarCodes={}",
                    calDTO.getRequestId(), calDTO.getLoanKey(), calDTO.getUserKey(), JSON.toJSONString(updateDto), groupId, JSON.toJSONString(curFinishVarCodes));
        }
        // 开始回调
        if (reqTask.getFinishVar().size() + reqTask.getErrorVar().size() == reqTask.getAllVar().size()) {
            prepareCallBack(updateDto, reqTask, dto, calDTO);
        }
        log.info("updateReqTask requestId: {}, groupId: {}, loanKey: {}, userKey: {} varCodes = {} allVar = {} ",task.getRequestId(),
                groupId, task.getLoanKey(), task.getUserKey(), reqTask.getFinishVar().size(), task.getAllVar().size());
    }

    /** 说明变量都计算完，开始回调 **/
    private void prepareCallBack(UpdateVariableReqTaskDTO updateDto, VariableReqTask task, HandlerResultDTO dto, VariableCalDTO calDTO) {
        List<String> eventSuffixs = NacosClientAdapter.getListConfig(NacosConfigConstants.NEED_WAIT_ALL_TO_SEND_EVENTS, String.class);

        // 分批回调
        boolean waitAllFlag = eventSuffixs.stream().anyMatch(e -> calDTO.getEventCode().contains(e));
        if (!waitAllFlag) {
            callBack(calDTO, task, task.getCallbackSystem(), dto.getVarResults());
        }

        Integer taskStatus = VariableReqTaskStatusEnum.SUCCESS.getCode();
        if (CollectionUtils.isNotEmpty(task.getErrorVar())) {
            taskStatus = VariableReqTaskStatusEnum.PART_SUCCESS.getCode();
        }
        updateDto.setStatus(taskStatus);
        reqTaskDAO.updateStatus(updateDto);

        if (!waitAllFlag) {
            return;
        }

        // 统一回调
        prepareCallBackALL(calDTO);
    }

    private void prepareCallBackALL(VariableDTO dto) {
        // 统一回调
        // 判断是否都已经处理过了 不管是否失败还是成功
        String redisKey = dto.getRequestId();
        List<VariableReqTask> reqTaskList = reqTaskDAO.selectAllStatusByReqId(dto.getRequestId());
        long count = reqTaskList.stream()
                .map(VariableReqTask::getStatus)
                .filter(status -> VariableReqTaskStatusEnum.INIT.getCode().equals(status) || VariableReqTaskStatusEnum.PROCESSING.getCode().equals(status))
                .count();

        if (count != 0L) {
            return;
        }

        // == 0 说明全部都已经处理好了
        try {
            // 获取锁 如果是成功则当前线程去处理，否者直接跳过
            if (redisService.lock(redisKey, "1", 5, TimeUnit.MINUTES)) {
                List<VariableReqTask> reqALLTaskList = reqTaskDAO.selectAllByReqId(dto.getRequestId());
                callBackAll(dto, reqALLTaskList);
            }
        } catch (Exception ex) {
            log.error("callBackAll CacheService tryLockAndSet error, e", ex);
        } finally {
            redisService.unlock(redisKey, "1");
        }
    }

    /**
     * 特殊处理
     * <AUTHOR>
     * @date 2024/5/16 20:07
     * @param dto
     * @param calDTO
     */
    private void specialProcess(HandlerResultDTO dto, VariableCalDTO calDTO) {
        try {
            ThreadPoolConstants.ALL_VARIABLE_THREAD_POOLS.execute(()->{
                sendRandomVarsToDC(dto, calDTO);
            });
        } catch (Exception e){
            log.error("sendRandomVarsToDC error", e);
        }

        /** 缺失率监控 **/
        if (CollectionUtils.isNotEmpty(dto.getVarResults())) {
            pointUtil.pointMissVariable(dto.getVarResults(), calDTO);
        }

        // 数据上报
        //直接上报数据, 不管是否计算完成，也不管一二期接口，也不管是否异步，因为表里最终也是拍平的
        variableReportService.reportData(calDTO, dto);
    }

    /**
     * 将随机数发送到DC
     * <AUTHOR>
     * @date  20:10
     * @param dto
     */
    private void sendRandomVarsToDC(HandlerResultDTO dto, VariableCalDTO calDTO) {
        boolean sendFlag = NacosClientAdapter.getBooleanConfig(NacosConfigConstants.SEND_RANDOM_FLAG_FLAG, false);
        if (!sendFlag) {
            return;
        }
        if (CollectionUtils.isEmpty(dto.getVarResults())) {
            return;
        }
        // 随机数都是原始变量
        if (!VariableTypeEnum.ORIGINAL.getCode().equals(calDTO.getVariableType())) {
            return;
        }
        //过滤对应数据源
        if (!"originInputService".equals(calDTO.getDataSourceCode()) && !"userBurstFlowService".equals(calDTO.getDataSourceCode())) {
            return;
        }
        Map<String, Object> map = dto.getVarResults().stream()
                .filter(v -> Constants.RANDOM_VARS.contains(v.getCode()))
                .collect(Collectors.toMap(v->v.getCode().substring(3), VariableResult::getValue));
        if (MapUtils.isEmpty(map)) {
            return;
        }
        if (map.get("random_mapping_id") != null) {
            map.put("mapping_id", map.get("random_mapping_id"));
            map.remove("random_mapping_id");
        }
        map.put("user_key", calDTO.getUserKey());
        log.info("sendRandomVarsToDC requestId: {}, loanKey: {}, userKey: {}, size:{}", calDTO.getRequestId(),
                calDTO.getLoanKey(), calDTO.getUserKey(), map.size());
        dataCenterInsideService.sendRandomData(JSON.toJSONString(map));
    }

    @Override
    public void throwableCallback(VariableDTO dto, Throwable throwable) {
        // 排除一期接口变量
        if (StringUtils.isNotBlank(dto.getVarGroupCode()) || StringUtils.isBlank(dto.getRequestId())) {
            log.info("throwableCallback requestId is blank, requestId:{}, loanKey:{}, userKey:{}", dto.getRequestId(), dto.getLoanKey(), dto.getUserKey());
            return;
        }
        List<String> groupIds = getGroupIds(dto, throwable);

        // 异常情况下如果是子模型 或者 子衍生 以及对应的基础变量的时候，需要将对应的模型和衍生变量找出进行回调
        List<String> relationVars = getDeriveAndModelVars(dto, throwable);
        // 如果是不需要回调的变量刚好分为一组了
        if (CollectionUtils.isEmpty(groupIds) && CollectionUtils.isEmpty(relationVars)) {
            log.info("throwableCallback groupId is blank, requestId:{}, loanKey:{}, userKey:{}, vars:{}",
                    dto.getRequestId(), dto.getLoanKey(), dto.getUserKey(), JSON.toJSONString(dto.getVarCodes()));
            return;
        }

        // 如果是异常回调需要将入口设置的锁给释放了,防止异常重试 失败
        cacheService.unlockRequestIdLock(dto.getRequestId());

        if (CollectionUtils.isNotEmpty(groupIds)) {
            for (String groupId : groupIds) {
                buildAndSendData(dto, groupId, throwable, null);
            }
        }

        if (CollectionUtils.isEmpty(relationVars)) {
            return;
        }

        List<String> relationGroupIds = relationVars.stream()
                .map(code -> cacheService.getDeriveVarGroupCache(dto.getLoanKey(), code))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        log.info("throwableCallback relationVars groupIds:{}, requestId:{}, loanKey:{}, userKey:{}, vars:{}",
                JSON.toJSONString(relationGroupIds), dto.getRequestId(), dto.getLoanKey(), dto.getUserKey(), JSON.toJSONString(relationVars));
        for (String groupId : relationGroupIds) {
            buildAndSendData(dto, groupId, throwable, relationVars);
        }
    }

    private void buildAndSendData(VariableDTO dto, String groupId, Throwable throwable, List<String> relationVars) {
        VariableReqTask task = reqTaskDAO.selectByReqIdAndGroupId(dto.getRequestId(), groupId);

        // 流水表没有的 说明不需要回调
        if (task == null) {
            log.info("throwableCallback task is blank, requestId:{}, loanKey:{}, userKey:{}, vars:{}",
                    dto.getRequestId(), dto.getLoanKey(), dto.getUserKey(), JSON.toJSONString(dto.getVarCodes()));
            return;
        }
        // 如果流水表已经回调过 就不回调了
        if (VariableReqTaskStatusEnum.FAIL.getCode().equals(task.getStatus())
                || VariableReqTaskStatusEnum.SUCCESS.getCode().equals(task.getStatus())
                || VariableReqTaskStatusEnum.PART_SUCCESS.getCode().equals(task.getStatus())) {
            log.info("throwableCallback task is callbacked, requestId:{}, loanKey:{}, userKey:{}, vars:{}",
                    dto.getRequestId(), dto.getLoanKey(), dto.getUserKey(), JSON.toJSONString(dto.getVarCodes()));
            return;
        }

        RiskCallBackDto callBaskDto;
        // 变量计算异常回调
        if (throwable instanceof VariableCalculateException && CollectionUtils.isEmpty(relationVars)) {
            callBaskDto = getVariableCalculateExceptionCallbackDto((VariableCalculateException) throwable, task, dto);
        } else if (CollectionUtils.isNotEmpty(relationVars)) { // 依赖异常变量的衍生模型变量回调
            callBaskDto = getDependExceptionVarsDeriveAndModelCallbackDto(throwable, task, dto, relationVars);
        } else {
            callBaskDto = getExceptionCallbackDto(throwable, task, dto, groupId);
        }

        List<String> eventSuffixs = NacosClientAdapter.getListConfig(NacosConfigConstants.NEED_WAIT_ALL_TO_SEND_EVENTS, String.class);
        boolean waitAllFlag = eventSuffixs.stream().anyMatch(e -> dto.getEventCode().contains(e));

        if (waitAllFlag) {
            // 统一回调
            prepareCallBackALL(dto);
            return;
        }

        if (callBaskDto == null) {
            return;
        }
        // 回调风控平台
        sendData(callBaskDto, dto, task.getCallbackSystem());
    }

    private RiskCallBackDto getExceptionCallbackDto(Throwable throwable, VariableReqTask task, VariableDTO dto, String groupId) {
        // 回调之前先更新流水表
        UpdateVariableReqTaskDTO updateDto = UpdateVariableReqTaskDTO.builder()
                .requestId(dto.getRequestId())
                .groupId(groupId)
                .status(VariableReqTaskStatusEnum.FAIL.getCode())
                .errorVars(task.getAllVar())
                .build();
        reqTaskDAO.updateByReqIdAndGroupId(updateDto);

        List<RiskCallBackDto.VariableInfo> variableList = task.getAllVar().stream()
                .map(var -> RiskCallBackDto.VariableInfo.builder()
                        .code(var)
                        .status(-1)
                        .message(throwable.getMessage())
                        .build())
                .collect(Collectors.toList());

        return RiskCallBackDto.builder()
                .requestId(dto.getRequestId())
                .variableList(variableList)
                .build();
    }

    private RiskCallBackDto getDependExceptionVarsDeriveAndModelCallbackDto(Throwable throwable, VariableReqTask task,
                                                                            VariableDTO dto, List<String> relationVars) {
        List<String> curErrorVarCodes = (List<String>) CollectionUtils.intersection(task.getAllVar(), relationVars);
        Map<String, String> curErrorMessages = curErrorVarCodes.stream().collect(Collectors.toMap(code -> code, v -> throwable.getMessage()));

        return getRiskCallBackDto(task, dto, curErrorVarCodes, curErrorMessages);
    }

    private RiskCallBackDto getVariableCalculateExceptionCallbackDto(VariableCalculateException variableCalculateException,
                                                                     VariableReqTask task, VariableDTO dto) {
        List<String> errorVarCodes = variableCalculateException.getErrorVarCodes();
        Map<String, String> errorMessages = variableCalculateException.getErrorMessages();

        List<String> curErrorVarCodes = errorVarCodes.stream().filter(task.getAllVar()::contains).collect(Collectors.toList());
        Map<String, String> curErrorMessages = errorMessages.entrySet().stream().
                filter(entry -> curErrorVarCodes.contains(entry.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        return getRiskCallBackDto(task, dto, curErrorVarCodes, curErrorMessages);
    }

    private RiskCallBackDto getRiskCallBackDto(VariableReqTask task, VariableDTO dto, List<String> curErrorVarCodes, Map<String, String> curErrorMessages) {
        VariableReqTask reqTask = throwableUpdateReqTask(task, curErrorVarCodes, curErrorMessages);
        if (reqTask.getFinishVar().size() + reqTask.getErrorVar().size() != reqTask.getAllVar().size()) {
            return null;
        }

        Integer taskStatus;
        if (CollectionUtils.isEmpty(reqTask.getFinishVar())) {
            taskStatus = VariableReqTaskStatusEnum.FAIL.getCode();
        } else {
            taskStatus = VariableReqTaskStatusEnum.PART_SUCCESS.getCode();
        }

        UpdateVariableReqTaskDTO updateDto = UpdateVariableReqTaskDTO.builder()
                .requestId(task.getRequestId()).groupId(task.getGroupId())
                .status(taskStatus)
                .build();
        reqTaskDAO.updateStatus(updateDto);

        task.getErrorVarMessage().putAll(curErrorMessages);
        Map<Object, Object> variableCache = redisService.hGetAll(dto.getLoanKey());

        List<RiskCallBackDto.VariableInfo> variableList = task.getAllVar().stream()
                .map(var -> RiskCallBackDto.VariableInfo.builder()
                        .code(var)
                        .status(task.getFinishVar().contains(var) ? 0 : -1)
                        .value(task.getFinishVar().contains(var) ? variableCache.get(var) : null)
                        .message(task.getFinishVar().contains(var) ? null : task.getErrorVarMessage().get(var))
                        .build())
                .collect(Collectors.toList());

        return RiskCallBackDto.builder()
                .requestId(dto.getRequestId())
                .variableList(variableList)
                .build();
    }

    private VariableReqTask throwableUpdateReqTask(VariableReqTask task, List<String> curErrorVarCodes, Map<String, String> errorMessages) {
        // 更新流水表
        UpdateVariableReqTaskDTO updateDto = UpdateVariableReqTaskDTO.builder()
                .requestId(task.getRequestId())
                .groupId(task.getGroupId())
                .status(VariableReqTaskStatusEnum.PROCESSING.getCode())
                .errorVars(curErrorVarCodes)
                .errorVarsMessages(errorMessages)
                .build();

        return reqTaskDAO.updateByReqIdAndGroupId(updateDto);
    }

    /**
     * requestId 分流做区分
     * @param dto
     * @return
     */
    private List<String> getDeriveAndModelVars(VariableDTO dto, Throwable throwable) {
        String requestId = dto.getRequestId();

        VariableDeriveRelationTask deriveTask = variableDeriveRelationTaskService.getDeriveRelationTask(requestId);
        if (deriveTask == null) {
            return Collections.emptyList();
        }

        List<String> errorVars;
        if (throwable instanceof VariableCalculateException) {
            errorVars = ((VariableCalculateException) throwable).getErrorVarCodes();
        } else {
            errorVars = dto.getVarCodes();
        }
        return getAllDeriveRelationVars(deriveTask, errorVars);
    }

    /**
     * 递归获取依赖当前变量所有的模型和样式变量
     * <AUTHOR>
     * @date 2024/7/30 14:37
     * @param deriveTask
     * @param vars
     * @return java.util.List<java.lang.String>
     */
    private List<String> getAllRelationVars(VariableDeriveTask deriveTask, List<String> vars) {
        List<String> relationVars = deriveTask.getDeriveRelation().entrySet().stream()
                .filter(entry -> vars.contains(entry.getKey()))
                .map(Map.Entry::getValue)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(relationVars)) {
            return Collections.emptyList();
        }
        // 递归获取所有的模型和衍生变量
        List<String> allRelationVars = getAllRelationVars(deriveTask, relationVars);
        if (CollectionUtils.isEmpty(allRelationVars)) {
            return new ArrayList<>(relationVars);
        }
        allRelationVars.addAll(relationVars);
        return allRelationVars.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 递归获取依赖当前变量所有的模型和衍生变量: 新的MONGO结构里取
     * <AUTHOR>
     * @date 2024/7/30 14:37
     * @param deriveTask
     * @param vars
     * @return java.util.List<java.lang.String>
     */
    private List<String> getAllDeriveRelationVars(VariableDeriveRelationTask deriveTask, List<String> vars) {
        Map<String, Map<String, Integer>> deriveRelationIndex = deriveTask.getDeriveRelationIndex();

        List<String> relationVars = new ArrayList<>();
        for (Map.Entry<String, Map<String, Integer>> entry : deriveRelationIndex.entrySet()){
            if (!vars.contains(entry.getKey())){
                continue;
            }
            /** 依赖当前变量所有的模型和衍生变量 **/
            Map<String, Integer> relationMap = entry.getValue();
            List<String> relationLists = relationMap.keySet().stream().collect(Collectors.toList());
            relationVars.addAll(relationLists);
        }
        /** 去重 **/
        relationVars = relationVars.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(relationVars)) {
            return Collections.emptyList();
        }
        // 递归获取所有的模型和衍生变量
        List<String> allRelationVars = getAllDeriveRelationVars(deriveTask, relationVars);
        if (CollectionUtils.isEmpty(allRelationVars)) {
            return new ArrayList<>(relationVars);
        }
        allRelationVars.addAll(relationVars);
        return allRelationVars.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 按组更新流程表
     * 因为一批衍生变量 可能会分为多组，因为同一个数据源会加工很多衍生变量
     * @param calDTO
     * @return
     */
    private List<String> getGroupIds(VariableDTO calDTO, Throwable throwable) {
        /** 衍生groupId特殊处理 **/
        if (VariableTypeEnum.DERIVE.getCode().equals(calDTO.getVariableType())
                || VariableTypeEnum.MODEL.getCode().equals(calDTO.getVariableType())) {
            if (throwable instanceof VariableCalculateException) {
                return ((VariableCalculateException) throwable).getErrorVarCodes().stream()
                        .map(code -> cacheService.getDeriveVarGroupCache(calDTO.getLoanKey(), code))
                        .filter(StringUtils::isNotBlank)
                        .distinct()
                        .collect(Collectors.toList());
            }
            // 异常情况下只有VarCodes, 正常情况下使用OriginVarCodes
            List<String> codes = CollectionUtils.isEmpty(calDTO.getOriginVarCodes()) ? calDTO.getVarCodes() : calDTO.getOriginVarCodes();
            return codes.stream()
                    .map(code -> cacheService.getDeriveVarGroupCache(calDTO.getLoanKey(), code))
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
        }

        return Lists.newArrayList(calDTO.getGroupId());
    }

    /**
     * 回调引擎
     *
     * @param varResults
     * @param calDTO
     */
    private void callBack(VariableCalDTO calDTO, VariableReqTask task, String callbackSystem, List<VariableResult> varResults) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("variable before callBack varResults requestId:{}, loanKey:{}, userKey:{}, allVarsSize:{}, varResults {} ",
                calDTO.getRequestId(), calDTO.getLoanKey(), calDTO.getUserKey(), task.getAllVar().size(),
                StringUtils.abbreviate(JSON.toJSONString(varResults), Constants.MAX_WIDTH));

        // 判断varResults 中是否都已经包含了需要回调的变量, 从缓存中获取
        List<RiskCallBackDto.VariableInfo> variableList = varResults.stream()
                .filter(var -> task.getAllVar().contains(var.getCode()))
                .map(var -> RiskCallBackDto.VariableInfo.builder()
                        .code(var.getCode())
                        .value(var.getValue())
                        .status(0)
                        .build())
                .collect(Collectors.toList());
        // 从缓存中获取
        if (variableList.size() != task.getAllVar().size()) {
            List<String> codes = variableList.stream()
                    .map(RiskCallBackDto.VariableInfo::getCode)
                    .collect(Collectors.toList());
            // 批量获取？
            List<RiskCallBackDto.VariableInfo> otherVars = task.getAllVar().stream()
                    .filter(var -> !codes.contains(var))
                    .map(var -> RiskCallBackDto.VariableInfo.builder()
                            .code(var)
                            .value(redisService.hGet(calDTO.getLoanKey(), var))
                            .status(task.getFinishVar().contains(var) ? 0 : -1)
                            .message(task.getFinishVar().contains(var) ? null : task.getErrorVarMessage().get(var))
                            .build())
                    .collect(Collectors.toList());
            variableList.addAll(otherVars);
        }

        RiskCallBackDto callBaskDto = RiskCallBackDto.builder()
                .requestId(calDTO.getRequestId())
                .variableList(variableList)
                .build();
        // 发送数据
        sendData(callBaskDto, calDTO, callbackSystem);

        log.info("variable after callBack cost:{}", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
    }

    private void callBackAll(VariableDTO dto, List<VariableReqTask> reqTaskList) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("variable before callBackAll requestId={}, loanKey={}, userKey={}", dto.getRequestId(), dto.getLoanKey(), dto.getUserKey());
        Map<Object, Object> variableCache = redisService.hGetAll(dto.getLoanKey());

        Map<String, ModelCallBackDto.ServiceData> serviceDataMap = new HashMap();
        List<ModelCallBackDto.VariableInfo> variableList = reqTaskList.stream()
                .map(task ->{
                    VariableInfo info = cacheService.getVarInfoByCode(task.getAllVar().get(0));
                    ReportMetaDataDTO reportMetaDataDTO = new ReportMetaDataDTO();
                    if (StringUtils.isNotBlank(info.getVariableSource())) {
                        reportMetaDataDTO = cacheService.getDataSourceJobId(dto.getLoanKey(),info.getVariableSource());
                    }

                    ReportMetaDataDTO finalReportMetaDataDTO = reportMetaDataDTO;
                    return task.getAllVar().stream()
                            .map(var -> {
                                serviceDataMap.put(var, ModelCallBackDto.ServiceData.builder()
                                        .jobId(finalReportMetaDataDTO.getJobId())
                                        .reqTimestamp(finalReportMetaDataDTO.getReqTimestamp())
                                        .serviceCode(info.getVariableSource())
                                        .build());

                                    return ModelCallBackDto.VariableInfo.builder()
                                            .code(var)
                                            .value(variableCache.get(var))
                                            .status(task.getFinishVar().contains(var) ? 0 : -1)
                                            .build();}
                        ).collect(Collectors.toList());
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        ModelCallBackDto callBaskDto = new ModelCallBackDto();
        callBaskDto.setRequestId(dto.getRequestId());
        callBaskDto.setVariableList(variableList);
        callBaskDto.setUserKey(dto.getUserKey());
        callBaskDto.setLoanKey(dto.getLoanKey());
        callBaskDto.setEventCode(dto.getEventCode());
        callBaskDto.setStrategyId(dto.getStrategyId());
        callBaskDto.setSourceSystem(dto.getSourceSystem());
        callBaskDto.setServiceData(serviceDataMap);

        // 发送数据
        sendData(callBaskDto, dto, reqTaskList.get(0).getCallbackSystem());

        boolean flag =NacosClientAdapter.getBooleanConfig(NacosConfigConstants.LOG_DETAIL, false);
        if (flag) {
            log.info("variable after callBackAll cost:{}, data:{}", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS), JSON.toJSONString(callBaskDto));
        } else {
            log.info("variable after callBackAll cost:{}", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        }
    }

    public void sendData(RiskCallBackDto callBaskDto, VariableDTO dto, String callbackSystem) {
        RiskCallbackSystemEnum systemEnum = RiskCallbackSystemEnum.getByName(callbackSystem);
        if (systemEnum == null) {
            log.error("postToRisk error, systemEnum is null, dto:{}", JSON.toJSONString(dto));
            throw new VariableException("callbackSystem:"+ callbackSystem +" is unknown");
        }

        if (systemEnum.getType() == RiskCallbackSystemEnum.CallbackTypeEnum.HTTP) {
            VariableServiceImpl self = (VariableServiceImpl) ApplicationContextUtils.getBean("variableServiceImpl");
            self.postToRisk(dto.getUserKey(), dto.getLoanKey(), callBaskDto, callbackSystem);
        } else if (systemEnum.getType() == RiskCallbackSystemEnum.CallbackTypeEnum.KAFKA) {
            log.info("sendData type is kafka, userKey: {}, loanKey: {}, reqId: {}", dto.getUserKey(), dto.getLoanKey(), dto.getRequestId());
            kafkaTemplate.send(systemEnum.getValue(),JSON.toJSONString(callBaskDto));
        } else {
            throw new VariableException("sendData type"+  systemEnum.getType() +" is unKonw");
        }

    }

    /**
     * 回调风控
     * @param callBaskDto
     */
    @Retryable(value = Exception.class, backoff = @Backoff(delay = 1000, multiplier = 1.5))
    public void postToRisk(String userKey, String loanKey, RiskCallBackDto callBaskDto, String callbackSystem) {
        RiskCallbackSystemEnum systemEnum = RiskCallbackSystemEnum.getByName(callbackSystem);
        String url = systemEnum.getValue();
        log.info("postToRisk start userKey:{},loanKey:{},  requestId:{}, callbackSystem:{} url:{}, data:{}",userKey, loanKey, callBaskDto.getRequestId(), callbackSystem,
                url, StringUtils.abbreviate(JSON.toJSONString(callBaskDto), Constants.MAX_WIDTH));
        Stopwatch stopwatch = Stopwatch.createStarted();
        String message = SyncHTTPRemoteAPI.postJson(url, JSON.toJSONString(callBaskDto), 30000);
        // TODO 对返回状态进行处理
        log.info("postToRisk end cost:{}, requestId: {}, message:{}", stopwatch.stop().elapsed(TimeUnit.MILLISECONDS),
                callBaskDto.getRequestId(), message);

        JSONObject jsonObject = JSONObject.parseObject(message);
        if (jsonObject == null || !Objects.equals(jsonObject.getInteger("status"), 0)) {
            log.error("postToRisk response error retry");
            throw new VariableException("postToRisk response error retry");
        }
        log.info("postToRisk success userKey:{},loanKey:{}, requestId: {}",userKey, loanKey, callBaskDto.getRequestId());
    }

    @Override
    public void checkReqTask() {
        List<VariableReqTask> reqTasks = reqTaskDAO.selectDelayReq();
        if (CollectionUtils.isEmpty(reqTasks)) {
            return;
        }

        log.info("checkReqTask task size:{}", reqTasks.size());
        Date now = new Date();
        Map<TimeDuration, Long> map = reqTasks.stream()
                .map(t-> (now.getTime() - t.getCreateTime().getTime())/ 1000/ 60)
                .map(time -> TimeDuration.getEnumByMinute(Math.toIntExact(time)))
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("变量未回调告警:\n");
        map.forEach((k, v) -> {
            stringBuilder.append(WeChatMarkDownLabel.LESS_THAN_SYMBOL).append(" ");
            stringBuilder.append(TimeDuration.getFontColor(k.getStart()));
            stringBuilder.append(k.getDesc()).append(v);
            stringBuilder.append(WeChatMarkDownLabel.FONT_END);
            stringBuilder.append("\n");
        });
        WeChatUtils.sendMarkDownMessage(stringBuilder.toString());
    }

    @Override
    public void checkKafkaRecordTask() {
        List<VarKafkaRecordRecordEntity> reqTasks = kafkaRecordDAO.selectDelayReq();
        if (CollectionUtils.isEmpty(reqTasks)) {
            return;
        }

        log.info("checkKafkaRecordTask task size:{}", reqTasks.size());
        Date now = new Date();
        Map<TimeDuration, Long> map = reqTasks.stream()
                .map(t-> (now.getTime() - t.getCreateTime().getTime())/ 1000/ 60)
                .map(time -> TimeDuration.getEnumByMinute(Math.toIntExact(time)))
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("归因未回调告警:\n");
        map.forEach((k, v) -> {
            stringBuilder.append(WeChatMarkDownLabel.LESS_THAN_SYMBOL).append(" ");
            stringBuilder.append(TimeDuration.getFontColor(k.getStart()));
            stringBuilder.append(k.getDesc()).append(v);
            stringBuilder.append(WeChatMarkDownLabel.FONT_END);
            stringBuilder.append("\n");
        });
        WeChatUtils.sendMarkDownMessage(stringBuilder.toString());
    }

}