<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weicai.mapper.UserBusinessLabelMapper">

    <resultMap id="BaseResultMap" type="com.weicai.entity.UserBusinessLabel">
            <id property="uid" column="uid" jdbcType="BIGINT"/>
            <result property="userLevel" column="user_level" jdbcType="VARCHAR"/>
            <result property="loanActualLine" column="loan_actual_line" jdbcType="DECIMAL"/>
            <result property="loanLine" column="loan_line" jdbcType="DECIMAL"/>
            <result property="isDiversion" column="is_diversion" jdbcType="INTEGER"/>
            <result property="accountStauts" column="account_stauts" jdbcType="INTEGER"/>
            <result property="registerTime" column="register_time" jdbcType="INTEGER"/>
            <result property="auditTime" column="audit_time" jdbcType="INTEGER"/>
            <result property="auditStatus" column="audit_status" jdbcType="INTEGER"/>
            <result property="payoffFlag" column="payoff_flag" jdbcType="INTEGER"/>
            <result property="firstLoanTime" column="first_loan_time" jdbcType="INTEGER"/>
            <result property="lastApplyTime" column="last_apply_time" jdbcType="INTEGER"/>
            <result property="lastTxxyTradeTime" column="last_txxy_trade_time" jdbcType="INTEGER"/>
            <result property="superMemberExpiredTime" column="super_member_expired_time" jdbcType="INTEGER"/>
            <result property="saveMoneyCardExpiredTime" column="save_money_card_expired_time" jdbcType="INTEGER"/>
            <result property="haoleCardExpiredTime" column="haole_card_expired_time" jdbcType="INTEGER"/>
            <result property="haojieCardExpiredTime" column="haojie_card_expired_time" jdbcType="INTEGER"/>
            <result property="isHaojieCardLabel" column="is_haojie_card_label" jdbcType="INTEGER"/>
            <result property="hisOverdueDays" column="his_overdue_days" jdbcType="INTEGER"/>
            <result property="platform" column="platform" jdbcType="VARCHAR"/>
            <result property="userStatus" column="user_status" jdbcType="VARCHAR"/>
            <result property="lastLoanAuditStatus" column="last_loan_audit_status" jdbcType="INTEGER"/>
            <result property="highestEducation" column="highest_education" jdbcType="INTEGER"/>
            <result property="irrYearRateGroup" column="irr_year_rate_group" jdbcType="INTEGER"/>
            <result property="isBusinessLicenseUploaded" column="is_business_license_uploaded" jdbcType="INTEGER"/>
            <result property="saveMoneyCardOpenTime" column="save_money_card_open_time" jdbcType="INTEGER"/>
            <result property="haoleCardOpenTime" column="haole_card_open_time" jdbcType="INTEGER"/>
            <result property="haojieCardOpenTime" column="haojie_card_open_time" jdbcType="INTEGER"/>
            <result property="auditEducation" column="audit_education" jdbcType="INTEGER"/>
            <result property="baoguoCardExpiredTime" column="baoguo_card_expired_time" jdbcType="INTEGER"/>
            <result property="baoguoCardOpenTime" column="baoguo_card_open_time" jdbcType="INTEGER"/>
            <result property="baoguoCardValidTime" column="baoguo_card_valid_time" jdbcType="INTEGER"/>
            <result property="baoguoCardState" column="baoguo_card_state" jdbcType="INTEGER"/>
            <result property="baoguoCardOrderSubmitTime" column="baoguo_card_order_submit_time" jdbcType="INTEGER"/>
            <result property="tiecardRiskTagOutputTime" column="tiecard_risk_tag_output_time" jdbcType="INTEGER"/>
            <result property="tiecardRiskTagVaildTime" column="tiecard_risk_tag_vaild_time" jdbcType="INTEGER"/>
            <result property="tiecardOrderSubmitTime" column="tiecard_order_submit_time" jdbcType="INTEGER"/>
            <result property="tiecardOrderPayTime" column="tiecard_order_pay_time" jdbcType="INTEGER"/>
            <result property="tiecardIncreaseSuccessTime" column="tiecard_increase_success_time" jdbcType="INTEGER"/>
            <result property="tiecardIncreaseFailTime" column="tiecard_increase_fail_time" jdbcType="INTEGER"/>
            <result property="tiecardAmountExpiredTime" column="tiecard_amount_expired_time" jdbcType="INTEGER"/>
            <result property="amountRetrialOutputTime" column="amount_retrial_output_time" jdbcType="INTEGER"/>
            <result property="amountRetrialSubmitTime" column="amount_retrial_submit_time" jdbcType="INTEGER"/>
            <result property="amountRetrialSuccessTime" column="amount_retrial_success_time" jdbcType="INTEGER"/>
            <result property="amountRetrialFailTime" column="amount_retrial_fail_time" jdbcType="INTEGER"/>
            <result property="wechatHfqUserStatus" column="wechat_hfq_user_status" jdbcType="INTEGER"/>
            <result property="biguoCardOpenTime" column="biguo_card_open_time" jdbcType="INTEGER"/>
            <result property="biguoCardOrderSubmitTime" column="biguo_card_order_submit_time" jdbcType="INTEGER"/>
            <result property="biguoCardTagStatus" column="biguo_card_tag_status" jdbcType="INTEGER"/>
            <result property="biguoCardExpiredTime" column="biguo_card_expired_time" jdbcType="INTEGER"/>
            <result property="biguoCardUserLevel" column="biguo_card_user_level" jdbcType="VARCHAR"/>
            <result property="isLogoffed" column="is_logoffed" jdbcType="INTEGER"/>
            <result property="jiangfeiCardFirstPayTime" column="jiangfei_card_first_pay_time" jdbcType="INTEGER"/>
            <result property="jiangfeiCardPayTime" column="jiangfei_card_pay_time" jdbcType="INTEGER"/>
            <result property="jiangfeiCardOrderSubmitTime" column="jiangfei_card_order_submit_time" jdbcType="INTEGER"/>
            <result property="jiangfeiCardOutputTime" column="jiangfei_card_output_time" jdbcType="INTEGER"/>
            <result property="jiangfeiCardCallbackTime" column="jiangfei_card_callback_time" jdbcType="INTEGER"/>
            <result property="jiangfeiCardExpiredTime" column="jiangfei_card_expired_time" jdbcType="INTEGER"/>
            <result property="jiangfeiCardTagStatus" column="jiangfei_card_tag_status" jdbcType="INTEGER"/>
            <result property="lastFixQuotaTime" column="last_fix_quota_time" jdbcType="INTEGER"/>
            <result property="lastTmpQuotaTime" column="last_tmp_quota_time" jdbcType="INTEGER"/>
            <result property="hasRepaytaskIncreaseQuotaTag" column="has_repaytask_increase_quota_tag" jdbcType="INTEGER"/>
            <result property="repaytaskIncreaseQuotaTagExpiredTime" column="repaytask_increase_quota_tag_expired_time" jdbcType="INTEGER"/>
            <result property="superMemberMemberType" column="super_member_member_type" jdbcType="INTEGER"/>
            <result property="superMemberAutoRenew" column="super_member_auto_renew" jdbcType="INTEGER"/>
            <result property="lastAuditSuccessTime" column="last_audit_success_time" jdbcType="INTEGER"/>
            <result property="lastUserLoanExpireTime" column="last_user_loan_expire_time" jdbcType="INTEGER"/>
            <result property="lastRepaySuccessTime" column="last_repay_success_time" jdbcType="INTEGER"/>
            <result property="userRandomMapId" column="user_random_map_id" jdbcType="INTEGER"/>
            <result property="isFirstLoan" column="is_first_loan" jdbcType="INTEGER"/>
            <result property="repaytaskIncreaseQuotaTagOutputTime" column="repaytask_increase_quota_tag_output_time" jdbcType="INTEGER"/>
            <result property="lastEducationIncreaseQuotaTime" column="last_education_increase_quota_time" jdbcType="INTEGER"/>
            <result property="currentPlatform" column="current_platform" jdbcType="VARCHAR"/>
            <result property="currentChannel" column="current_channel" jdbcType="VARCHAR"/>
            <result property="lastDcCarSubmitTime" column="last_dc_car_submit_time" jdbcType="INTEGER"/>
            <result property="lastIncreaseQuotaLiftAmount" column="last_increase_quota_lift_amount" jdbcType="DECIMAL"/>
            <result property="lastIncreaseQuotaExpiredTime" column="last_increase_quota_expired_time" jdbcType="BIGINT"/>
            <result property="dcApiStatus" column="dc_api_status" jdbcType="INTEGER"/>
            <result property="jiasuCardPayTime" column="jiasu_card_pay_time" jdbcType="INTEGER"/>
            <result property="jiasuCardExpireTime" column="jiasu_card_expire_time" jdbcType="INTEGER"/>
            <result property="userSource" column="user_source" jdbcType="VARCHAR"/>
            <result property="userType" column="user_type" jdbcType="VARCHAR"/>
            <result property="userOriginSource" column="user_origin_source" jdbcType="VARCHAR"/>
            <result property="userSourceFirstName" column="user_source_first_name" jdbcType="VARCHAR"/>
            <result property="userSourceSecondName" column="user_source_second_name" jdbcType="VARCHAR"/>
            <result property="isNewLoan" column="is_new_loan" jdbcType="INTEGER"/>
            <result property="loanSubmitNumber" column="loan_submit_number" jdbcType="INTEGER"/>
            <result property="superMemberPaymentFailureTime" column="super_member_payment_failure_time" jdbcType="INTEGER"/>
            <result property="availableCouponTypes" column="available_coupon_types" jdbcType="VARCHAR"/>
            <result property="haojieCardAutopayStatus" column="haojie_card_autopay_status" jdbcType="INTEGER"/>
            <result property="lastSupplementContactTime" column="last_supplement_contact_time" jdbcType="INTEGER"/>
            <result property="currentFirstActiveTime" column="current_first_active_time" jdbcType="INTEGER"/>
            <result property="lastActiveTime" column="last_active_time" jdbcType="INTEGER"/>
            <result property="lastFixJiangfeiTime" column="last_fix_jiangfei_time" jdbcType="INTEGER"/>
            <result property="lastTmpJiangfeiTime" column="last_tmp_jiangfei_time" jdbcType="INTEGER"/>
            <result property="lastVisitAppTime" column="last_visit_app_time" jdbcType="INTEGER"/>
            <result property="isOperateProxyUser" column="is_operate_proxy_user" jdbcType="INTEGER"/>
            <result property="minUnpayoffDueDate" column="min_unpayoff_due_date" jdbcType="INTEGER"/>
            <result property="minLpTimePerday" column="min_lp_time_perday" jdbcType="INTEGER"/>
            <result property="lastLoanTime" column="last_loan_time" jdbcType="INTEGER"/>
            <result property="lastEntrepreneurIncreaseQuotaTime" column="last_entrepreneur_increase_quota_time" jdbcType="INTEGER"/>
            <result property="lastPersontaxIncreaseQuotaTime" column="last_persontax_increase_quota_time" jdbcType="INTEGER"/>
            <result property="lastAlipayIncreaseQuotaTime" column="last_alipay_increase_quota_time" jdbcType="INTEGER"/>
            <result property="lastAccumulationfundIncreaseQuotaTime" column="last_accumulationfund_increase_quota_time" jdbcType="INTEGER"/>
            <result property="userRandomMapId1" column="user_random_map_id1" jdbcType="INTEGER"/>
            <result property="userRandomMapId2" column="user_random_map_id2" jdbcType="INTEGER"/>
            <result property="userRandomMapId3" column="user_random_map_id3" jdbcType="INTEGER"/>
            <result property="userRandomMapId4" column="user_random_map_id4" jdbcType="INTEGER"/>
            <result property="userRandomMapId5" column="user_random_map_id5" jdbcType="INTEGER"/>
            <result property="userRandomMapId6" column="user_random_map_id6" jdbcType="INTEGER"/>
            <result property="newCusGrayscaleTag" column="new_cus_grayscale_tag" jdbcType="INTEGER"/>
            <result property="brMultLoanAlsM3IdNbankOrgnum" column="br_mult_loan_als_m3_id_nbank_orgnum" jdbcType="INTEGER"/>
            <result property="brMultLoanCurrentTime" column="br_mult_loan_current_time" jdbcType="INTEGER"/>
            <result property="wjqseAiCurrentTime" column="wjqse_ai_current_time" jdbcType="INTEGER"/>
            <result property="jobNum" column="job_num" jdbcType="INTEGER"/>
            <result property="monthIncomeNum" column="month_income_num" jdbcType="INTEGER"/>
            <result property="irrYearRateValue" column="irr_year_rate_value" jdbcType="INTEGER"/>
            <result property="complaintLevel" column="complaint_level" jdbcType="VARCHAR"/>
            <result property="complaintSource" column="complaint_source" jdbcType="VARCHAR"/>
            <result property="complaintSpectacle" column="complaint_spectacle" jdbcType="VARCHAR"/>
            <result property="rtBrMultLoanAlsM3IdNbankOrgnum" column="rt_br_mult_loan_als_m3_id_nbank_orgnum" jdbcType="INTEGER"/>
            <result property="rtBrMultLoanAlsM3IdNbankTime" column="rt_br_mult_loan_als_m3_id_nbank_time" jdbcType="INTEGER"/>
            <result property="brPreLoanAlsM3CellNbankOrgnum" column="br_pre_loan_als_m3_cell_nbank_orgnum" jdbcType="INTEGER"/>
            <result property="rtBrPreLoanAlsM3CellNbankOrgnum" column="rt_br_pre_loan_als_m3_cell_nbank_orgnum" jdbcType="INTEGER"/>
            <result property="lastBankCardFailedTime" column="last_bank_card_failed_time" jdbcType="INTEGER"/>
            <result property="telesaleBlacklistExpiredTime" column="telesale_blacklist_expired_time" jdbcType="BIGINT"/>
            <result property="superMemberPaymentSuccessTime" column="super_member_payment_success_time" jdbcType="INTEGER"/>
            <result property="isRegisterUnloanMarketing" column="is_register_unloan_marketing" jdbcType="INTEGER"/>
            <result property="enterSupervipPageCnt" column="enter_supervip_page_cnt" jdbcType="INTEGER"/>
            <result property="enterProductlistPageTime" column="enter_productlist_page_time" jdbcType="INTEGER"/>
            <result property="enterBtnapiPageTime" column="enter_btnapi_page_time" jdbcType="INTEGER"/>
            <result property="enterSupervipPaymentPageTime" column="enter_supervip_payment_page_time" jdbcType="INTEGER"/>
            <result property="baiweiRegStatus" column="baiwei_reg_status" jdbcType="VARCHAR"/>
            <result property="weihengLevel" column="weiheng_level" jdbcType="VARCHAR"/>
            <result property="brSdScoredrzRzRadical" column="br_sd_scoredrz_rz_radical" jdbcType="VARCHAR"/>
            <result property="lastSuperMemberOrderTime" column="last_super_member_order_time" jdbcType="INTEGER"/>
            <result property="lastMonthCardEndTime" column="last_month_card_end_time" jdbcType="INTEGER"/>
            <result property="lastFixQuotaLine" column="last_fix_quota_line" jdbcType="DECIMAL"/>
            <result property="lastTmpQuotaLine" column="last_tmp_quota_line" jdbcType="DECIMAL"/>
            <result property="lastTmpLineEndTime" column="last_tmp_line_end_time" jdbcType="INTEGER"/>
            <result property="userKey" column="user_key" jdbcType="VARCHAR"/>
            <result property="regMobile" column="reg_mobile" jdbcType="VARCHAR"/>
            <result property="idName" column="id_name" jdbcType="VARCHAR"/>
            <result property="diversionAbtest" column="diversion_abtest" jdbcType="INTEGER"/>
            <result property="originFirstName" column="origin_first_name" jdbcType="VARCHAR"/>
            <result property="originSecondName" column="origin_second_name" jdbcType="VARCHAR"/>
            <result property="jpAppInstalledList" column="jp_app_installed_list" jdbcType="VARCHAR"/>
            <result property="manageLevel3" column="manage_level_3" jdbcType="VARCHAR"/>
            <result property="isShieldDxdx" column="is_shield_dxdx" jdbcType="INTEGER"/>
            <result property="isShieldDxaiwh" column="is_shield_dxaiwh" jdbcType="INTEGER"/>
            <result property="isShieldDxrgwh" column="is_shield_dxrgwh" jdbcType="INTEGER"/>
            <result property="auditFailExpirationTime" column="audit_fail_expiration_time" jdbcType="BIGINT"/>
            <result property="lastLoanFailTime" column="last_loan_fail_time" jdbcType="INTEGER"/>
            <result property="ctlYx01Proba" column="ctl_yx01_proba" jdbcType="DECIMAL"/>
            <result property="aiYx01Proba" column="ai_yx01_proba" jdbcType="DECIMAL"/>
            <result property="manualYx01Proba" column="manual_yx01_proba" jdbcType="DECIMAL"/>
            <result property="lastCreditByApiChannel" column="last_credit_by_api_channel" jdbcType="VARCHAR"/>
            <result property="regMobileCity" column="reg_mobile_city" jdbcType="VARCHAR"/>
            <result property="apiEarlistPeriod" column="api_earlist_period" jdbcType="INTEGER"/>
            <result property="apiEarlistRate" column="api_earlist_rate" jdbcType="DECIMAL"/>
            <result property="apiLastTotalAmount" column="api_last_total_amount" jdbcType="DECIMAL"/>
            <result property="apiLastAvailAmount" column="api_last_avail_amount" jdbcType="DECIMAL"/>
            <result property="apiLastUsedAmount" column="api_last_used_amount" jdbcType="DECIMAL"/>
            <result property="apiLastActionTime" column="api_last_action_time" jdbcType="INTEGER"/>
            <result property="apiLastMarketingRejTag" column="api_last_marketing_rej_tag" jdbcType="INTEGER"/>
            <result property="apiLastLiftAmount" column="api_last_lift_amount" jdbcType="DECIMAL"/>
            <result property="apiLastLiftStgyTime" column="api_last_lift_stgy_time" jdbcType="INTEGER"/>
            <result property="apiHitLibraryCnts1d" column="api_hit_library_cnts_1d" jdbcType="INTEGER"/>
            <result property="apiHitLibraryCnts7d" column="api_hit_library_cnts_7d" jdbcType="INTEGER"/>
            <result property="dcApiStatus" column="dc_api_status" jdbcType="INTEGER"/>
            <result property="lastSuperMemberOrderTime" column="last_super_member_order_time" jdbcType="INTEGER"/>
            <result property="lastMonthCardEndTime" column="last_month_card_end_time" jdbcType="INTEGER"/>
            <result property="saMemViewCnt10d" column="sa_mem_view_cnt_10d" jdbcType="INTEGER"/>
            <result property="incomeTag" column="income_tag" jdbcType="INTEGER"/>
            <result property="smeFlag" column="sme_flag" jdbcType="INTEGER"/>
            <result property="isAlipaySign" column="is_alipay_sign" jdbcType="INTEGER"/>
            <result property="apiLastAuditTime" column="api_last_audit_time" jdbcType="INTEGER"/>
            <result property="apiLastAuditStatus" column="api_last_audit_status" jdbcType="INTEGER"/>
            <result property="apiLastAuditAssetCode" column="api_last_audit_asset_code" jdbcType="VARCHAR"/>
            <result property="apiLastAuditSuccessTime" column="api_last_audit_success_time" jdbcType="INTEGER"/>
            <result property="apiLastApplyTime" column="api_last_apply_time" jdbcType="INTEGER"/>
            <result property="apiLastLoanStatus" column="api_last_loan_status" jdbcType="INTEGER"/>
            <result property="apiLastApplyAssetCode" column="api_last_apply_asset_code" jdbcType="VARCHAR"/>
            <result property="apiLastEmitTime" column="api_last_emit_time" jdbcType="INTEGER"/>
            <result property="regMobileProvince" column="reg_mobile_province" jdbcType="VARCHAR"/>
            <result property="idNoProvince" column="id_no_province" jdbcType="VARCHAR"/>
            <result property="apiGpsLastPosProvinceName" column="api_gps_last_pos_province_name" jdbcType="VARCHAR"/>
            <result property="isYxjrGroup" column="is_yxjr_group" jdbcType="INTEGER"/>
            <result property="yixinLastHitFailTime" column="yixin_last_hit_fail_time" jdbcType="INTEGER"/>
            <result property="saHjkViewCnt20d" column="sa_hjk_view_cnt_20d" jdbcType="INTEGER"/>
            <result property="firstOcrTime" column="first_ocr_time" jdbcType="INTEGER"/>
            <result property="firstBandCardTime" column="first_band_card_time" jdbcType="INTEGER"/>
            <result property="lastAuditCreateTime" column="last_audit_create_time" jdbcType="INTEGER"/>
            <result property="lastAuditFailedTime" column="last_audit_failed_time" jdbcType="INTEGER"/>
            <result property="lastEnterWithdrawTime" column="last_enter_withdraw_time" jdbcType="INTEGER"/>
            <result property="lastLoanSubmitTime" column="last_loan_submit_time" jdbcType="INTEGER"/>
            <result property="isMemberPingbi" column="is_member_pingbi" jdbcType="INTEGER"/>
            <result property="isHjkPingbi" column="is_hjk_pingbi" jdbcType="INTEGER"/>
            <result property="refundCntJ180" column="refund_cnt_j180" jdbcType="INTEGER"/>
            <result property="refundCntJ90" column="refund_cnt_j90" jdbcType="INTEGER"/>
            <result property="huiyuankaTfLevelAll" column="huiyuanka_tf_level_all" jdbcType="INTEGER"/>
            <result property="mxBaRfdU1t7VipR01Score" column="mx_ba_rfd_u1t7_vip_r01_score" jdbcType="DECIMAL"/>
            <result property="mxBaRfdU1t7HjkR01Score" column="mx_ba_rfd_u1t7_hjk_r01_score" jdbcType="DECIMAL"/>
            <result property="haojiekaTfLevelAll" column="haojieka_tf_level_all" jdbcType="INTEGER"/>
            <result property="isHfqLoanUser" column="is_hfq_loan_user" jdbcType="INTEGER"/>
            <result property="urLoanInfoMob" column="ur_loan_info_mob" jdbcType="INTEGER"/>
            <result property="hfqFirstLoanSuccessTime" column="hfq_first_loan_success_time" jdbcType="INTEGER"/>
            <result property="saLastEnterWithdrawTime" column="sa_last_enter_withdraw_time" jdbcType="BIGINT"/>
            <result property="loanFailExpirationTime" column="loan_fail_expiration_time" jdbcType="BIGINT"/>
            <result property="lastLiftStgyTime" column="last_lift_stgy_time" jdbcType="BIGINT"/>
            <result property="lastLiftType" column="last_lift_type" jdbcType="VARCHAR"/>
            <result property="lastLiftStgyAmt" column="last_lift_stgy_amt" jdbcType="DECIMAL"/>
            <result property="lastLiftLineInvalidStartTime" column="last_lift_line_invalid_start_time" jdbcType="BIGINT"/>
            <result property="lastLiftLineInvalidLastTime" column="last_lift_line_invalid_last_time" jdbcType="BIGINT"/>
            <result property="lastLiftAmtTypeDetail" column="last_lift_amt_type_detail" jdbcType="VARCHAR"/>
            <result property="lastLiftTmpLineEndTime" column="last_lift_tmp_line_end_time" jdbcType="BIGINT"/>
            <result property="lastRoutineLiftStgyTime" column="last_routine_lift_stgy_time" jdbcType="BIGINT"/>
            <result property="lastRoutineLiftType" column="last_routine_lift_type" jdbcType="VARCHAR"/>
            <result property="lastRoutineLiftStgyAmt" column="last_routine_lift_stgy_amt" jdbcType="DECIMAL"/>
            <result property="lastRoutineLineInvalidStartTime" column="last_routine_line_invalid_start_time" jdbcType="BIGINT"/>
            <result property="lastRoutineLineInvalidLastTime" column="last_routine_line_invalid_last_time" jdbcType="BIGINT"/>
            <result property="lastRoutineLiftAmtTypeDetail" column="last_routine_lift_amt_type_detail" jdbcType="VARCHAR"/>
            <result property="lastRoutineTmpLineEndTime" column="last_routine_tmp_line_end_time" jdbcType="BIGINT"/>
            <result property="lastMiddleDeriveLiftStgyTime" column="last_middle_derive_lift_stgy_time" jdbcType="BIGINT"/>
            <result property="lastMiddleDeriveLiftType" column="last_middle_derive_lift_type" jdbcType="VARCHAR"/>
            <result property="lastMiddleDeriveLiftStgyAmt" column="last_middle_derive_lift_stgy_amt" jdbcType="DECIMAL"/>
            <result property="lastMiddleDeriveLineInvalidStartTime" column="last_middle_derive_line_invalid_start_time" jdbcType="BIGINT"/>
            <result property="lastMiddleDeriveLineInvalidLastTime" column="last_middle_derive_line_invalid_last_time" jdbcType="BIGINT"/>
            <result property="lastMiddleDeriveLiftAmtTypeDetail" column="last_middle_derive_lift_amt_type_detail" jdbcType="VARCHAR"/>
            <result property="lastMiddleDeriveTmpLineEndTime" column="last_middle_derive_tmp_line_end_time" jdbcType="BIGINT"/>
            <result property="lastActiDeriveLiftStgyTime" column="last_acti_derive_lift_stgy_time" jdbcType="BIGINT"/>
            <result property="lastActiDeriveLiftType" column="last_acti_derive_lift_type" jdbcType="VARCHAR"/>
            <result property="lastActiDeriveLiftStgyAmt" column="last_acti_derive_lift_stgy_amt" jdbcType="DECIMAL"/>
            <result property="lastActiDeriveLineInvalidStartTime" column="last_acti_derive_line_invalid_start_time" jdbcType="BIGINT"/>
            <result property="lastActiDeriveLineInvalidLastTime" column="last_acti_derive_line_invalid_last_time" jdbcType="BIGINT"/>
            <result property="lastActiDeriveLiftAmtTypeDetail" column="last_acti_derive_lift_amt_type_detail" jdbcType="VARCHAR"/>
            <result property="lastActiDeriveTmpLineEndTime" column="last_acti_derive_tmp_line_end_time" jdbcType="BIGINT"/>
            <result property="tiecardRiskTagLiftAmountAvail" column="tiecard_risk_tag_lift_amount_avail" jdbcType="DECIMAL"/>
            <result property="isLawyerLetterPlan" column="is_lawyer_letter_plan" jdbcType="INTEGER"/>
            <result property="lastStopCollectionTime" column="last_stop_collection_time" jdbcType="BIGINT"/>
            <result property="lastCollectionsStep" column="last_collections_step" jdbcType="VARCHAR"/>
            <result property="dhRandom52" column="dh_random_52" jdbcType="INTEGER"/>
            <result property="dhRandom90" column="dh_random_90" jdbcType="INTEGER"/>
            <result property="lastLawyerLetterSendTime" column="last_lawyer_letter_send_time" jdbcType="BIGINT"/>
            <result property="apiFirstEmitSuccessTime" column="api_first_emit_success_time" jdbcType="BIGINT"/>
            <result property="apiLastAppEmitSuccessTime" column="api_last_app_emit_success_time" jdbcType="BIGINT"/>
            <result property="apiDzLoginAmountTime" column="api_dz_login_amount_time" jdbcType="BIGINT"/>
            <result property="apiLastRiskConversionLevel" column="api_last_risk_conversion_level" jdbcType="VARCHAR"/>
            <result property="apiLastBorrowFailedTime" column="api_last_borrow_failed_time" jdbcType="BIGINT"/>
            <result property="apiLastSuccessApiPartnerCode" column="api_last_success_api_partner_code" jdbcType="VARCHAR"/>
            <result property="apiFirstAppReturnDaysCnt" column="api_first_app_return_days_cnt" jdbcType="INTEGER"/>
            <result property="lastApiLoginAmountTime" column="last_api_login_amount_time" jdbcType="BIGINT"/>
            <result property="lastApiLoginAmountStatus" column="last_api_login_amount_status" jdbcType="INTEGER"/>
            <result property="lastApiRepaymentRequestTime" column="last_api_repayment_request_time" jdbcType="BIGINT"/>
            <result property="pidBirthDay" column="pid_birth_day" jdbcType="VARCHAR"/>
            <result property="userPidType" column="user_pid_type" jdbcType="INTEGER"/>
            <result property="changeAmountFix30d" column="change_amount_fix_30d" jdbcType="DECIMAL"/>
            <result property="changeAmountTemp30d" column="change_amount_temp_30d" jdbcType="DECIMAL"/>
            <result property="changeAmountFixAndTemp30d" column="change_amount_fix_and_temp_30d" jdbcType="DECIMAL"/>
            <result property="userGender" column="user_gender" jdbcType="INTEGER"/>
            <result property="marriageEnum" column="marriage_enum" jdbcType="VARCHAR"/>
            <result property="idNoCity" column="id_no_city" jdbcType="VARCHAR"/>
            <result property="apiGpsLastPosCityName" column="api_gps_last_pos_city_name" jdbcType="VARCHAR"/>
            <result property="lastCollectionsType" column="last_collections_type" jdbcType="VARCHAR"/>
            <result property="dhRandom20" column="dh_random_20" jdbcType="INTEGER"/>
            <result property="dhRandom97" column="dh_random_97" jdbcType="INTEGER"/>
            <result property="isLawyerLetterUrl" column="is_lawyer_letter_url" jdbcType="INTEGER"/>
        <result property="lastWhiteCreateTime180d" column="last_white_create_time_180d" jdbcType="BIGINT"/>
        <result property="lastSupplementCreateTime180d" column="last_supplement_create_time_180d" jdbcType="BIGINT"/>
        <result property="lastActivisionCreateTime180d" column="last_activision_create_time_180d" jdbcType="BIGINT"/>
        <result property="lastAutoCreateTime180d" column="last_auto_create_time_180d" jdbcType="BIGINT"/>
        <result property="userRegFirstName" column="user_reg_first_name" jdbcType="VARCHAR"/>
        <result property="userRegSecondName" column="user_reg_second_name" jdbcType="VARCHAR"/>
        <result property="fixedOriginName" column="fixed_origin_name" jdbcType="VARCHAR"/>
        <result property="fixedFirstAgentName" column="fixed_first_agent_name" jdbcType="VARCHAR"/>
        <result property="fixedAgentName" column="fixed_agent_name" jdbcType="VARCHAR"/>
        <result property="appFirstLoginTime" column="app_first_login_time" jdbcType="BIGINT"/>
        <result property="appFirstEnterAuditTime" column="app_first_enter_audit_time" jdbcType="BIGINT"/>
        <result property="appFirstAuditSuccessTime" column="app_first_audit_success_time" jdbcType="BIGINT"/>
        <result property="appFirstWithdrawTime" column="app_first_withdraw_time" jdbcType="BIGINT"/>
        <result property="appFirstEmitTime" column="app_first_emit_time" jdbcType="BIGINT"/>
        <result property="lastMpDeriveCreateTime180d" column="last_mp_derive_create_time_180d" jdbcType="BIGINT"/>
        <result property="mxYxRuaUas16t30AllG01Proba" column="mx_yx_rua_uas16t30_all_g01_proba" jdbcType="DECIMAL"/>
        <result property="mxYxRuaUas31t60AllG01Proba" column="mx_yx_rua_uas31t60_all_g01_proba" jdbcType="DECIMAL"/>
        <result property="isHoufuBlackList" column="is_houfu_black_list" jdbcType="INTEGER"/>
        <result property="lastTiecardType" column="last_tiecard_type" jdbcType="INTEGER"/>
        <result property="dhRandom91" column="dh_random_91" jdbcType="INTEGER"/>
        <result property="lastMpFirstloginTagInvalidtime180d" column="last_mp_firstlogin_tag_invalidtime_180d" jdbcType="BIGINT"/>
        <result property="lastMpFirstloginTagAmount180d" column="last_mp_firstlogin_tag_amount_180d" jdbcType="DECIMAL"/>
        <result property="mpFirstloginLastLiftDate180d" column="mp_firstlogin_last_lift_date_180d" jdbcType="BIGINT"/>
        <result property="mxBaCjhyUb7AllV01Proba" column="mx_ba_cjhy_ub7_all_v01_proba" jdbcType="DECIMAL"/>
        <result property="mxBaHjkUb7AllV01Proba" column="mx_ba_hjk_ub7_all_v01_proba" jdbcType="DECIMAL"/>

    </resultMap>

    <sql id="Base_Column_List">
        uid,user_level,loan_actual_line,
        loan_line,is_diversion,account_stauts,
        register_time,audit_time,audit_status,
        payoff_flag,first_loan_time,last_apply_time,
        last_txxy_trade_time,super_member_expired_time,save_money_card_expired_time,
        haole_card_expired_time,haojie_card_expired_time,is_haojie_card_label,
        his_overdue_days,platform,user_status,
        last_loan_audit_status,highest_education,irr_year_rate_group,
        is_business_license_uploaded,save_money_card_open_time,haole_card_open_time,
        haojie_card_open_time,audit_education,baoguo_card_expired_time,
        baoguo_card_open_time,baoguo_card_valid_time,baoguo_card_state,
        baoguo_card_order_submit_time,tiecard_risk_tag_output_time,tiecard_risk_tag_vaild_time,
        tiecard_order_submit_time,tiecard_order_pay_time,tiecard_increase_success_time,
        tiecard_increase_fail_time,tiecard_amount_expired_time,amount_retrial_output_time,
        amount_retrial_submit_time,amount_retrial_success_time,amount_retrial_fail_time,
        wechat_hfq_user_status,biguo_card_open_time,biguo_card_order_submit_time,
        biguo_card_tag_status,biguo_card_expired_time,biguo_card_user_level,
        is_logoffed,jiangfei_card_first_pay_time,jiangfei_card_pay_time,
        jiangfei_card_order_submit_time,jiangfei_card_output_time,jiangfei_card_callback_time,
        jiangfei_card_expired_time,jiangfei_card_tag_status,last_fix_quota_time,
        last_tmp_quota_time,has_repaytask_increase_quota_tag,repaytask_increase_quota_tag_expired_time,
        super_member_member_type,super_member_auto_renew,last_audit_success_time,
        last_user_loan_expire_time,last_repay_success_time,user_random_map_id,
        is_first_loan,repaytask_increase_quota_tag_output_time,last_education_increase_quota_time,
        current_platform,current_channel,last_dc_car_submit_time,
        last_increase_quota_lift_amount,last_increase_quota_expired_time,dc_api_status,
        jiasu_card_pay_time,jiasu_card_expire_time,user_source,
        user_type,user_origin_source,user_source_first_name,
        user_source_second_name,is_new_loan,loan_submit_number,
        super_member_payment_failure_time,available_coupon_types,haojie_card_autopay_status,
        last_supplement_contact_time,current_first_active_time,last_active_time,
        last_fix_jiangfei_time,last_tmp_jiangfei_time,last_visit_app_time,
        is_operate_proxy_user,min_unpayoff_due_date,min_lp_time_perday,
        last_loan_time,last_entrepreneur_increase_quota_time,last_persontax_increase_quota_time,
        last_alipay_increase_quota_time,last_accumulationfund_increase_quota_time,user_random_map_id1,
        user_random_map_id2,user_random_map_id3,user_random_map_id4,
        user_random_map_id5,user_random_map_id6,new_cus_grayscale_tag,
        br_mult_loan_als_m3_id_nbank_orgnum,
        br_mult_loan_current_time,wjqse_ai_current_time,job_num,
        month_income_num,irr_year_rate_value,
        complaint_level,complaint_source,complaint_spectacle,
        rt_br_mult_loan_als_m3_id_nbank_orgnum,rt_br_mult_loan_als_m3_id_nbank_time,
        br_pre_loan_als_m3_cell_nbank_orgnum,rt_br_pre_loan_als_m3_cell_nbank_orgnum,last_bank_card_failed_time,
        telesale_blacklist_expired_time,super_member_payment_success_time,is_register_unloan_marketing,
        enter_supervip_page_cnt,enter_productlist_page_time,enter_btnapi_page_time,
        enter_supervip_payment_page_time,baiwei_reg_status,weiheng_level,
        br_sd_scoredrz_rz_radical,last_super_member_order_time,last_month_card_end_time,
        last_fix_quota_line,last_tmp_quota_line,last_tmp_line_end_time,user_key,reg_mobile,id_name,diversion_abtest,origin_first_name,origin_second_name,
        is_shield_dxdx,is_shield_dxaiwh,is_shield_dxrgwh,jp_app_installed_list,manage_level_3,audit_fail_expiration_time,last_loan_fail_time,last_credit_by_api_channel,
        reg_mobile_city,api_earlist_period,api_earlist_rate,api_last_total_amount,api_last_avail_amount,api_last_used_amount,api_last_action_time,api_last_marketing_rej_tag,api_last_lift_amount,
        api_last_lift_stgy_time,api_hit_library_cnts_1d,api_hit_library_cnts_7d,dc_api_status,
        last_super_member_order_time,last_month_card_end_time,sa_mem_view_cnt_10d,income_tag,sme_flag,is_alipay_sign,
        api_last_audit_time,api_last_audit_status,api_last_audit_asset_code,api_last_audit_success_time,api_last_apply_time,
        api_last_loan_status,api_last_apply_asset_code,api_last_emit_time,reg_mobile_province,id_no_province,
        api_gps_last_pos_province_name,is_yxjr_group,yixin_last_hit_fail_time,sa_hjk_view_cnt_20d,first_ocr_time,
        first_band_card_time,last_audit_create_time,last_audit_failed_time,last_enter_withdraw_time,last_loan_submit_time,
        is_member_pingbi,is_hjk_pingbi,refund_cnt_j180,refund_cnt_j90,huiyuanka_tf_level_all,
        mx_ba_rfd_u1t7_vip_r01_score,mx_ba_rfd_u1t7_hjk_r01_score,haojieka_tf_level_all,is_hfq_loan_user,
        ur_loan_info_mob,hfq_first_loan_success_time,sa_last_enter_withdraw_time,loan_fail_expiration_time,last_lift_stgy_time,
        last_lift_type,last_lift_stgy_amt,last_lift_line_invalid_start_time,last_lift_line_invalid_last_time,last_lift_amt_type_detail,last_lift_tmp_line_end_time,last_routine_lift_stgy_time,last_routine_lift_type,
        last_routine_lift_stgy_amt,last_routine_line_invalid_start_time,last_routine_line_invalid_last_time,last_routine_lift_amt_type_detail,last_routine_tmp_line_end_time,last_middle_derive_lift_stgy_time,
        last_middle_derive_lift_type,last_middle_derive_lift_stgy_amt,last_middle_derive_line_invalid_start_time,last_middle_derive_line_invalid_last_time,last_middle_derive_lift_amt_type_detail,last_middle_derive_tmp_line_end_time,
        last_acti_derive_lift_stgy_time,last_acti_derive_lift_type,last_acti_derive_lift_stgy_amt,last_acti_derive_line_invalid_start_time,last_acti_derive_line_invalid_last_time,last_acti_derive_lift_amt_type_detail,last_acti_derive_tmp_line_end_time,
        tiecard_risk_tag_lift_amount_avail,is_lawyer_letter_plan,last_stop_collection_time,last_collections_step,dh_random_52,dh_random_90,last_lawyer_letter_send_time,api_first_emit_success_time,api_last_app_emit_success_time,api_dz_login_amount_time,
        api_last_risk_conversion_level,api_last_borrow_failed_time,api_last_success_api_partner_code,api_first_app_return_days_cnt,last_api_login_amount_time,last_api_login_amount_status,last_api_repayment_request_time,pid_birth_day,user_pid_type,
        change_amount_fix_30d,change_amount_temp_30d,change_amount_fix_and_temp_30d,user_gender,marriage_enum,id_no_city,api_gps_last_pos_city_name,last_collections_type,dh_random_20,dh_random_97,is_lawyer_letter_url,
        last_white_create_time_180d,last_supplement_create_time_180d,last_activision_create_time_180d,last_auto_create_time_180d,
        user_reg_first_name,user_reg_second_name,fixed_origin_name,fixed_first_agent_name,fixed_agent_name,app_first_login_time,app_first_enter_audit_time,app_first_audit_success_time,app_first_withdraw_time,app_first_emit_time,last_mp_derive_create_time_180d,mx_yx_rua_uas16t30_all_g01_proba,mx_yx_rua_uas31t60_all_g01_proba,is_houfu_black_list,last_tiecard_type,dh_random_91,
           last_mp_firstlogin_tag_invalidtime_180d,last_mp_firstlogin_tag_amount_180d,mp_firstlogin_last_lift_date_180d,mx_ba_cjhy_ub7_all_v01_proba,mx_ba_hjk_ub7_all_v01_proba
    </sql>
</mapper>
