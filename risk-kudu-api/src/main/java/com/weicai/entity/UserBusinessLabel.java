package com.weicai.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * @TableName user_business_label
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_business_label")
public class UserBusinessLabel implements Serializable {

    /**
     * 用户id
     */
    @TableId
    private Long uid;

    /**
     * 风险等级
     */
    private String userLevel;

    /**
     * 可用额度
     */
    private BigDecimal loanActualLine;

    /**
     * 授信额度
     */
    private BigDecimal loanLine;

    /**
     * 是否可导流（1：是，0：否）
     */
    private Integer isDiversion;

    /**
     * 账户状态，0异常1初始状态2正常3逾期4失效5禁用6坏账
     */
    private Integer accountStauts;

    /**
     * 注册时间
     */
    private Integer registerTime;

    /**
     * 最近一次进件时间
     */
    private Integer auditTime;

    /**
     * 审核状态，1：审核中 2：通过 3：失败
     */
    private Integer auditStatus;

    /**
     * 全部结清，0：未结清，1：已结清 2：未签约
     */
    private Integer payoffFlag;

    /**
     * 第一笔放款时间（计算账龄）
     */
    private Integer firstLoanTime;

    /**
     * 最近一次申请（签约）时间
     */
    private Integer lastApplyTime;

    /**
     * 天下信用最近一次购买时间
     */
    private Integer lastTxxyTradeTime;

    /**
     * 超级会员过期时间(为null时表示未购买)
     */
    private Integer superMemberExpiredTime;

    /**
     * 省钱卡过期时间(为null时表示未购买
     */
    private Integer saveMoneyCardExpiredTime;

    /**
     * 好乐卡过期时间(为null时表示未购买)
     */
    private Integer haoleCardExpiredTime;

    /**
     * 好借卡过期时间(为null时表示未购买)
     */
    private Integer haojieCardExpiredTime;

    /**
     * 是否好借卡标签用户（1：是，0：否）
     */
    private Integer isHaojieCardLabel;

    /**
     * 历史最大逾期天数
     */
    private Integer hisOverdueDays;

    /**
     * 操作系统
     */
    private String platform;

    /**
     * 用户状态
     */
    private String userStatus;

    /**
     * 最近1次签约的放款审核状态 1:通过 0:拒绝
     */
    private Integer lastLoanAuditStatus;

    /**
     * 最高学历
     */
    private Integer highestEducation;

    /**
     * IRR综合年化费率 0: 未知 1: ≤IRR24% 2: (IRR24%，IRR36] 3: ＞IRR36%
     */
    private Integer irrYearRateGroup;

    /**
     * 是否上传营业执照 -1: 否(信息有缺失) 0:否(信息无缺失) 1:是
     */
    private Integer isBusinessLicenseUploaded;

    /**
     * 省钱卡最近一次开卡时间(为null时表示未购买)
     */
    private Integer saveMoneyCardOpenTime;

    /**
     * 好乐卡最近一次开卡时间(为null时表示未购买)
     */
    private Integer haoleCardOpenTime;

    /**
     * 好借卡最近一次开卡时间(为null时表示未购买)
     */
    private Integer haojieCardOpenTime;

    /**
     * 进件学历(最高)
     */
    private Integer auditEducation;

    /**
     * 保过卡最近一次过期时间(为null时表示未购买)
     */
    private Integer baoguoCardExpiredTime;

    /**
     * 保过卡最近一次开卡时间(为null时表示未购买)
     */
    private Integer baoguoCardOpenTime;

    /**
     * 保过卡有效时间 
     */
    private Integer baoguoCardValidTime;

    /**
     * 保过卡状态
     */
    private Integer baoguoCardState;

    /**
     * 生成保过(过审)卡订单时间
     */
    private Integer baoguoCardOrderSubmitTime;

    /**
     * 风控输出提额卡标签时间
     */
    private Integer tiecardRiskTagOutputTime;

    /**
     * 风控输出提额卡标签有效期
     */
    private Integer tiecardRiskTagVaildTime;

    /**
     * 生成提额卡订单时间
     */
    private Integer tiecardOrderSubmitTime;

    /**
     * 提额卡支付时间
     */
    private Integer tiecardOrderPayTime;

    /**
     * 提额卡提额成功时间
     */
    private Integer tiecardIncreaseSuccessTime;

    /**
     * 提额卡提额失败时间
     */
    private Integer tiecardIncreaseFailTime;

    /**
     * 提额卡失效时间
     */
    private Integer tiecardAmountExpiredTime;

    /**
     * 最近一次贷中额度重审输出时间
     */
    private Integer amountRetrialOutputTime;

    /**
     * 最近一次提交贷中额度重审时间
     */
    private Integer amountRetrialSubmitTime;

    /**
     * 最近一次贷中额度重审成功时间
     */
    private Integer amountRetrialSuccessTime;

    /**
     * 最近一次贷中额度重审失败时间
     */
    private Integer amountRetrialFailTime;

    /**
     * 好分期微信用户状态 null：未绑定 0：绑定但未关注公众号 1：绑定且关注公众号
     */
    private Integer wechatHfqUserStatus;

    /**
     * 必过卡购买成功时间
     */
    private Integer biguoCardOpenTime;

    /**
     * 必过卡支付页进入时间（生成订单时间）
     */
    private Integer biguoCardOrderSubmitTime;

    /**
     * 必过卡状态 1：有效 2：用户购买成功 3：审核拒绝退费 4：放款失败退费 5：客服操作退费
     */
    private Integer biguoCardTagStatus;

    /**
     * 必过卡失效时间
     */
    private Integer biguoCardExpiredTime;

    /**
     * 用户等级
     */
    private String biguoCardUserLevel;

    /**
     * 用户是否已注销1：是， 0:否
     */
    private Integer isLogoffed;

    /**
     * 首次降费卡成功购买时间
     */
    private Integer jiangfeiCardFirstPayTime;

    /**
     * 降费卡成功购买时间
     */
    private Integer jiangfeiCardPayTime;

    /**
     * 降费卡成功下单时间
     */
    private Integer jiangfeiCardOrderSubmitTime;

    /**
     * 降费卡输出标签时间
     */
    private Integer jiangfeiCardOutputTime;

    /**
     * 降费卡更新标签时间  
     */
    private Integer jiangfeiCardCallbackTime;

    /**
     * 降费卡有效期标签时间
     */
    private Integer jiangfeiCardExpiredTime;

    /**
     * 降费卡标签的使用状态 0：未使用1：已使用
     */
    private Integer jiangfeiCardTagStatus;

    /**
     * 最近一次风控固定提额时间
     */
    private Integer lastFixQuotaTime;

    /**
     * 最近一次风控临时提额时间
     */
    private Integer lastTmpQuotaTime;

    /**
     * 是否还款提额标签  0:否 1:是
     */
    private Integer hasRepaytaskIncreaseQuotaTag;

    /**
     * 还款提额标签过期时间 
     */
    private Integer repaytaskIncreaseQuotaTagExpiredTime;

    /**
     * 超级会员会员类型 0年卡  1季卡
     */
    private Integer superMemberMemberType;

    /**
     * 超级会员是否自动续费 1是 0否
     */
    private Integer superMemberAutoRenew;

    /**
     * 最近一次成功进件时间
     */
    private Integer lastAuditSuccessTime;

    /**
     * 基于user_loan表的最后应还款时间
     */
    private Integer lastUserLoanExpireTime;

    /**
     * 最近一次还款成功时间
     */
    private Integer lastRepaySuccessTime;

    /**
     * 基线标签map id
     */
    private Integer userRandomMapId;

    /**
     * 首复贷区分标签 0复贷 1首贷
     */
    private Integer isFirstLoan;

    /**
     * 最新一次输出临转固标签时间
     */
    private Integer repaytaskIncreaseQuotaTagOutputTime;

    /**
     * 最近一次提交过学籍提额时间
     */
    private Integer lastEducationIncreaseQuotaTime;

    /**
     * 当前用户使用的操作系统 Android/iPhone
     */
    private String currentPlatform;

    /**
     * 当前用户使用的渠道
     */
    private String currentChannel;

    /**
     * 最近一次在易鑫车贷提交资料时间
     */
    private Integer lastDcCarSubmitTime;

    /**
     * 最近一笔提额额度
     */
    private BigDecimal lastIncreaseQuotaLiftAmount;

    /**
     * 最近一笔提额额度有效期
     */
    private Long lastIncreaseQuotaExpiredTime;

    /**
     * 贷超API状态
     */
    private Integer dcApiStatus;

    /**
     * 加速卡成功购买时间
     */
    private Integer jiasuCardPayTime;

    /**
     * 加速卡标签有效期时间
     */
    private Integer jiasuCardExpireTime;

    /**
     * 用户渠道
     */
    private String userSource;

    /**
     * 用户渠道类型
     */
    private String userType;

    /**
     * 用户组合渠道
     */
    private String userOriginSource;

    /**
     * 用户一级渠道名称
     */
    private String userSourceFirstName;

    /**
     * 用户二级渠道名称
     */
    private String userSourceSecondName;

    /**
     * 新老客标签 新客1 老客0
     */
    private Integer isNewLoan;

    /**
     * 累计提交借款笔数
     */
    private Integer loanSubmitNumber;

    /**
     * 最近购买会员支付失败时间
     */
    private Integer superMemberPaymentFailureTime;

    /**
     * 可用优惠券类型
     */
    private String availableCouponTypes;

    /**
     * 好借卡自动续费状态 0: 未自动续费 1: 自动续费
     */
    private Integer haojieCardAutopayStatus;

    /**
     * 最近一次补充联系人时间
     */
    private Integer lastSupplementContactTime;

    /**
     * 当日首次登录时间
     */
    private Integer currentFirstActiveTime;

    /**
     * 最近一次登录时间
     */
    private Integer lastActiveTime;

    /**
     * 风控最近一次固定降费时间
     */
    private Integer lastFixJiangfeiTime;

    /**
     * 风控最近一次临时降费时间
     */
    private Integer lastTmpJiangfeiTime;

    /**
     * 最近1次App访问时间（T+1口径）
     */
    private Integer lastVisitAppTime;

    /**
     * 是否代运营的运营期用户 1:是，0:不是
     */
    private Integer isOperateProxyUser;

    /**
     * 最近一个应还款日
     */
    private Integer minUnpayoffDueDate;

    /**
     * 用户当日首次进入借款页时间
     */
    private Integer minLpTimePerday;

    /**
     * 最后一笔成功放款时间
     */
    private Integer lastLoanTime;

    /**
     * 最近一次提交 企业主 提额的日期
     */
    private Integer lastEntrepreneurIncreaseQuotaTime;

    /**
     * 最近一次提交 个税 提额的日期
     */
    private Integer lastPersontaxIncreaseQuotaTime;

    /**
     * 最近一次提交 支付宝 提额的日期
     */
    private Integer lastAlipayIncreaseQuotaTime;

    /**
     * 最近一次提交 公积金 提额的日期
     */
    private Integer lastAccumulationfundIncreaseQuotaTime;

    /**
     * 业务端基线标签map id1
     */
    private Integer userRandomMapId1;

    /**
     * 业务端基线标签map id2
     */
    private Integer userRandomMapId2;

    /**
     * 业务端基线标签map id3
     */
    private Integer userRandomMapId3;

    /**
     * 业务端基线标签map id4
     */
    private Integer userRandomMapId4;

    /**
     * 业务端基线标签map id5
     */
    private Integer userRandomMapId5;

    /**
     * 业务端基线标签map id6
     */
    private Integer userRandomMapId6;

    /**
     * 新客提额2.0放量标签 对照组: 0,实验组: 1
     */
    private Integer newCusGrayscaleTag;

    /**
     * 近3个月在非银机构申请机构数
     */
    private Integer brMultLoanAlsM3IdNbankOrgnum;


    /**
     * 百融计算的当前时间戳
     */
    private Integer brMultLoanCurrentTime;

    /**
     *  计算营销AI分的当前时间戳
     */
    private Integer wjqseAiCurrentTime;

    /**
     * 职业枚举值
     */
    private Integer jobNum;

    /**
     * 月收入枚举值
     */
    private Integer monthIncomeNum;

    /**
     * IRR综合年化费率
     */
    private Integer irrYearRateValue;

    /**
     * 投诉等级
     */
    private String complaintLevel;

    /**
     * 投诉来源
     */
    private String complaintSource;

    /**
     * 投诉场景
     */
    private String complaintSpectacle;

    /**
     * 授信多头，近3个月在非银机构申请机构数
     */
    private Integer rtBrMultLoanAlsM3IdNbankOrgnum;

    /**
     * 授信多头，近3个月在非银机构申请机构时间
     */
    private Integer rtBrMultLoanAlsM3IdNbankTime;

    /**
     * 注册多头，近3个月在非银机构申请机构数
     */
    private Integer brPreLoanAlsM3CellNbankOrgnum;

    /**
     * 注册多头，近3个月在非银机构申请机构数
     */
    private Integer rtBrPreLoanAlsM3CellNbankOrgnum;

    /**
     * 最近1次因银行卡异常放款失败的时间（电销标签迁移）
     */
    private Integer lastBankCardFailedTime;

    /**
     * 电销黑名单最晚过期时间（电销标签迁移）
     */
    private Long telesaleBlacklistExpiredTime;

    /**
     * 最近一次会员支付成功时间
     */
    private Integer superMemberPaymentSuccessTime;

    /**
     * 若用户为短信撞库注册未进件营销用户，则为1，否则为0
     */
    private Integer isRegisterUnloanMarketing;

    /**
     * 用户最近10天内会员页浏览次数
     */
    private Integer enterSupervipPageCnt;

    /**
     * 用户最近一次进入贷超列表页时间(神策数据保留3天)
     */
    private Integer enterProductlistPageTime;

    /**
     * 用户最近一次点击贷超API签约页确认借款btn时间(神策数据保留3天)
     */
    private Integer enterBtnapiPageTime;

    /**
     * 最近一次进入会员确认支付页时间(神策数据保留3天)
     */
    private Integer enterSupervipPaymentPageTime;

    /**
     * 企业经营状态
     */
    private String baiweiRegStatus;

    /**
     * 卫诚征信返回数据（用于是否有个税数据）
     */
    private String weihengLevel;

    /**
     * 百融评分积极指数（用于是否有个税数据）
     */
    private String brSdScoredrzRzRadical;

    /**
     * 最近提交季/年卡会员订单时间
     */
    private Integer lastSuperMemberOrderTime;

    /**
     * 百是否购买过体验月卡
     */
    private Integer lastMonthCardEndTime;

    /**
     * 好借_固定提额额度
     */
    private BigDecimal lastFixQuotaLine;

    /**
     * 好借_临时提额额度
     */
    private BigDecimal lastTmpQuotaLine;

    /**
     * 好借_临时提额到期日
     */
    private Integer lastTmpLineEndTime;

    /**
     * 用户key
     */
    private String userKey;

    /**
     * 注册手机号
     */
    private String regMobile;

    /**
     * id姓名
     */
    private String idName;

    /**
     * 联合经营的上下展示
     */
    private Integer diversionAbtest;

    /**
     * 归因一级渠道
     */
    private String originFirstName;

    /**
     * 归因二级渠道
     */
    private String originSecondName;

    /**
     * 是否要求屏蔽电销短信
     */
    private Integer isShieldDxdx;

    /**
     * 是否要求屏蔽电销AI外呼
     */
    private Integer isShieldDxaiwh;

    /**
     * 是否要求屏蔽电销人工外呼
     */
    private Integer isShieldDxrgwh;

    /**
     * applist
     */
    private String jpAppInstalledList;

    @TableField("manage_level_3")
    private String manageLevel3;

    /**
     * 进件审核被拒锁定到期时间
     */
    private Long auditFailExpirationTime;

    /**
     * 最近一次放款审核失败时间
     */
    private Integer lastLoanFailTime;

    // https://www.tapd.cn/48583176/prong/stories/view/1148583176001028815?from=wxnotification&corpid=ww63478d6a674cf652&agentid=1000024&jump_count=1&qy_private_corpid=
    /**
     * 对照组模型预测概率
     */
    private BigDecimal ctlYx01Proba;

    /**
     * ai营销模型预测概率
     */
    private BigDecimal aiYx01Proba;

    /**
     * 人工营销模型预测概率
     */
    private BigDecimal manualYx01Proba;

    /**
     * 最近一次授信通过的API渠道
     */
    private String lastCreditByApiChannel;
    /**
     * 注册手机号归属城市
     */
    private String regMobileCity;
    /**
     * API用户最小期次
     */
    private Integer apiEarlistPeriod;
    /**
     * API用户最小期次的费率
     */
    private BigDecimal apiEarlistRate;
    /**
     * API用户最近一次的总额度
     */
    private BigDecimal apiLastTotalAmount;
    /**
     * API用户最近一次的可用额度
     */
    private BigDecimal apiLastAvailAmount;
    /**
     * API用户最近一次的已用额度
     */
    private BigDecimal apiLastUsedAmount;
    /**
     * API用户提额金额
     */
    private BigDecimal apiLastLiftAmount;
    /**
     * API用户最近一次的撞库时间
     */
    private Integer apiLastActionTime;
    /**
     * API用户是否可营销
     */
    private Integer apiLastMarketingRejTag;
    /**
     * API用户提额时间
     */
    private Integer apiLastLiftStgyTime;
    /**
     * API用户最近1天撞库次数
     */
    @TableField("api_hit_library_cnts_1d")
    private Integer apiHitLibraryCnts1d;
    /**
     * API用户最近7天撞库次数
     */
    @TableField("api_hit_library_cnts_7d")
    private Integer apiHitLibraryCnts7d;

    /**
     * 过去10天浏览会员弹窗次数
     */
    @TableField("sa_mem_view_cnt_10d")
    private Integer saMemViewCnt10d;

    /**
     * 用户是否有个税数据
     */
    private Integer incomeTag;

    /**
     * 用户名下企业经营状态是否为 存续|在业|开业
     */
    private Integer smeFlag;

    /**
     * 是否支付宝签约
     */
    private Integer isAlipaySign;

    /**
     * 合作方用户最近一次进件时间
     */
    private Integer apiLastAuditTime;

    /**
     * 合作方用户最近一次进件状态
     */
    private Integer apiLastAuditStatus;

    /**
     * 合作方用户最近一次进件合作方
     */
    private String apiLastAuditAssetCode;

    /**
     * 合作方用户最近一次授信时间
     */
    private Integer apiLastAuditSuccessTime;

    /**
     * 合作方用户最近一次申请时间
     */
    private Integer apiLastApplyTime;

    /**
     * 合作方用户最近一次申请状态
     */
    private Integer apiLastLoanStatus;

    /**
     * 合作方用户最近一次申请合作方
     */
    private String apiLastApplyAssetCode;

    /**
     * 合作方用户最近一次放款时间
     */
    private Integer apiLastEmitTime;

    /**
     * 注册手机号归属省份
     */
    private String regMobileProvince;

    /**
     * 身份证号码归属省份
     */
    private String idNoProvince;

    /**
     * GPS最近一次采集省份名称
     */
    private String apiGpsLastPosProvinceName;

    /**
     * 是否易鑫金融客群
     */
    private Integer isYxjrGroup;

    /**
     * 易鑫最近1次撞库失败时间
     */
    private Integer yixinLastHitFailTime;

    /**
     * 过去20天浏览好借卡弹窗次数
     */
    @TableField("sa_hjk_view_cnt_20d")
    private Integer saHjkViewCnt20d;

    /**
     * 首次OCR认证通过
     */
    private Integer firstOcrTime;

    /**
     * 首次绑定银行卡通过
     */
    private Integer firstBandCardTime;

    /**
     * 最近一次提交进件时间
     */
    private Integer lastAuditCreateTime;

    /**
     * 最近一个授信失败的时间
     */
    private Integer lastAuditFailedTime;

    /**
     * 最后一次进入签约页时间
     */
    private Integer lastEnterWithdrawTime;

    /**
     * 最新一次提交借款请求(所有资料已补充并提交)
     */
    private Integer lastLoanSubmitTime;

    /**
     * 是否会员屏蔽
     */
    private Integer isMemberPingbi;

    /**
     * 是否好借卡屏蔽
     */
    private Integer isHjkPingbi;

    /**
     * 会员好借卡180天退费次数
     */
    @TableField("refund_cnt_j180")
    private Integer refundCntJ180;

    /**
     * 会员好借卡90天退费次数
     */
    @TableField("refund_cnt_j90")
    private Integer refundCntJ90;

    /**
     * 会员退费等级
     */
    private Integer huiyuankaTfLevelAll;

    /**
     * 会员退费模型分
     */
    private BigDecimal mxBaRfdU1t7VipR01Score;

    /**
     * 好借卡退费模型分
     */
    private BigDecimal mxBaRfdU1t7HjkR01Score;

    /**
     * 好借卡退费等级
     */
    @TableField("haojieka_tf_level_all")
    private Integer haojiekaTfLevelAll;

    /**
     * 是否好分期放款用户
     */
    private Integer isHfqLoanUser;

    /**
     * 账龄
     */
    private Integer urLoanInfoMob;

    /**
     * 好分期首次借款成功时间
     */
    private Integer hfqFirstLoanSuccessTime;

    /**埋点最近一次进入签约页时间*/
    private Long saLastEnterWithdrawTime;
    /**放款审核被拒锁定期时间*/
    private Long loanFailExpirationTime;
    /**最近一次提额时间*/
    private Long lastLiftStgyTime;
    /**最近一次提额类型*/
    private String lastLiftType;
    /**最近一次提额金额*/
    private BigDecimal lastLiftStgyAmt;
    /**最近一次提额开始执行失效时间*/
    private Long lastLiftLineInvalidStartTime;
    /**最近一次提额最终执行失效时间*/
    private Long lastLiftLineInvalidLastTime;
    /**最近一次提额类型细分*/
    private String lastLiftAmtTypeDetail;
    /**最近一次临时提额到期日*/
    private Long lastLiftTmpLineEndTime;
    /**最近一次提额时间(风控常规提额)*/
    private Long lastRoutineLiftStgyTime;
    /**最近一次提额类型(风控常规提额)*/
    private String lastRoutineLiftType;
    /**最近一次提额金额(风控常规提额)*/
    private BigDecimal lastRoutineLiftStgyAmt;
    /**最近一次开始执行失效时间(风控常规提额)*/
    private Long lastRoutineLineInvalidStartTime;
    /**最近一次最终执行失效时间(风控常规提额)*/
    private Long lastRoutineLineInvalidLastTime;
    /**最近一次提额类型细分(风控常规提额)*/
    private String lastRoutineLiftAmtTypeDetail;
    /**最近一次临时提额到期日(风控常规提额)*/
    private Long lastRoutineTmpLineEndTime;
    /**最近一次提额时间(风控贷中提额)*/
    private Long lastMiddleDeriveLiftStgyTime;
    /**最近一次提额类型(风控贷中提额)*/
    private String lastMiddleDeriveLiftType;
    /**最近一次提额金额(风控贷中提额)*/
    private BigDecimal lastMiddleDeriveLiftStgyAmt;
    /**最近一次开始执行失效时间(风控贷中提额)*/
    private Long lastMiddleDeriveLineInvalidStartTime;
    /**最近一次最终执行失效时间(风控贷中提额)*/
    private Long lastMiddleDeriveLineInvalidLastTime;
    /**最近一次提额类型细分(风控贷中提额)*/
    private String lastMiddleDeriveLiftAmtTypeDetail;
    /**最近一次临时提额到期日(风控贷中提额)*/
    private Long lastMiddleDeriveTmpLineEndTime;
    /**最近一次提额时间(风控全局促活)*/
    private Long lastActiDeriveLiftStgyTime;
    /**最近一次提额类型(风控全局促活)*/
    private String lastActiDeriveLiftType;
    /**最近一次提额金额(风控全局促活)*/
    private BigDecimal lastActiDeriveLiftStgyAmt;
    /**最近一次开始执行失效时间(风控全局促活)*/
    private Long lastActiDeriveLineInvalidStartTime;
    /**最近一次最终执行失效时间(风控全局促活)*/
    private Long lastActiDeriveLineInvalidLastTime;
    /**最近一次提额类型细分(风控全局促活)*/
    private String lastActiDeriveLiftAmtTypeDetail;
    /**最近一次临时提额到期日(风控全局促活)*/
    private Long lastActiDeriveTmpLineEndTime;
    /**风控输出提额卡可提额度*/
    private BigDecimal tiecardRiskTagLiftAmountAvail;

    /**是否计划发送律师函*/
    private Integer isLawyerLetterPlan;
    /**最近停催时间*/
    private Long lastStopCollectionTime;
    /**催收队列*/
    private String lastCollectionsStep;
    /**随机数52*/
    @TableField("dh_random_52")
    private Integer dhRandom52;
    /**随机数90*/
    @TableField("dh_random_90")
    private Integer dhRandom90;
    /**最近一次实际发送律师函时间*/
    private Long lastLawyerLetterSendTime;
    /**端外首次放款成功时间*/
    private Long apiFirstEmitSuccessTime;
    /**端外首次放款成功后最近一次APP放款成功时间*/
    private Long apiLastAppEmitSuccessTime;
    /**API回端跑批提额成功时间*/
    private Long apiDzLoginAmountTime;
    /**风险转化等级*/
    private String apiLastRiskConversionLevel;
    /**最近一次api借款失败时间*/
    private Long apiLastBorrowFailedTime;
    /**最近一次放款成功的平台*/
    private String apiLastSuccessApiPartnerCode;
    private Integer apiFirstAppReturnDaysCnt;
    private Long lastApiLoginAmountTime;
    private Integer lastApiLoginAmountStatus;
    private Long lastApiRepaymentRequestTime;
    private String pidBirthDay;
    private Integer userPidType;
    @TableField("change_amount_fix_30d")
    private BigDecimal changeAmountFix30d;
    @TableField("change_amount_temp_30d")
    private BigDecimal changeAmountTemp30d;
    @TableField("change_amount_fix_and_temp_30d")
    private BigDecimal changeAmountFixAndTemp30d;
    private Integer userGender;
    private String marriageEnum;
    private String idNoCity;
    private String apiGpsLastPosCityName;
    private String lastCollectionsType;
    @TableField("dh_random_20")
    private Integer dhRandom20;
    @TableField("dh_random_97")
    private Integer dhRandom97;
    private Integer isLawyerLetterUrl;
    @TableField("last_white_create_time_180d")
    private Long lastWhiteCreateTime180d;
    @TableField("last_supplement_create_time_180d")
    private Long lastSupplementCreateTime180d;
    @TableField("last_activision_create_time_180d")
    private Long lastActivisionCreateTime180d;
    @TableField("last_auto_create_time_180d")
    private Long lastAutoCreateTime180d;
    private String userRegSecondName;
    private String userRegFirstName;
    private String fixedOriginName;
    private String fixedFirstAgentName;
    private String fixedAgentName;
    private Long appFirstLoginTime;
    private Long appFirstEnterAuditTime;
    private Long appFirstAuditSuccessTime;
    private Long appFirstWithdrawTime;
    private Long appFirstEmitTime;
    @TableField("last_mp_derive_create_time_180d")
    private Long lastMpDeriveCreateTime180d;
    @TableField("mx_yx_rua_uas16t30_all_g01_proba")
    private BigDecimal mxYxRuaUas16t30AllG01Proba;
    @TableField("mx_yx_rua_uas31t60_all_g01_proba")
    private BigDecimal mxYxRuaUas31t60AllG01Proba;
    private Integer isHoufuBlackList;
    private Integer lastTiecardType;
    @TableField("dh_random_91")
    private Integer dhRandom91;
    @TableField("last_mp_firstlogin_tag_invalidtime_180d")
    private Long lastMpFirstloginTagInvalidtime180d;
    @TableField("last_mp_firstlogin_tag_amount_180d")
    private BigDecimal lastMpFirstloginTagAmount180d;
    @TableField("mp_firstlogin_last_lift_date_180d")
    private Long mpFirstloginLastLiftDate180d;
    @TableField("mx_ba_cjhy_ub7_all_v01_proba")
    private BigDecimal mxBaCjhyUb7AllV01Proba;
    @TableField("mx_ba_hjk_ub7_all_v01_proba")
    private BigDecimal mxBaHjkUb7AllV01Proba;

}